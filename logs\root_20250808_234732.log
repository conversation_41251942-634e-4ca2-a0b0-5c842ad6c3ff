2025-08-08 23:47:33 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-08 23:47:34 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-08 23:47:34 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-08 23:47:34 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-08 23:47:34 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-08 23:47:34 - GUI - INFO - GUI界面初始化完成
2025-08-08 23:47:52 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250808_230919
2025-08-08 23:47:52 - session_loader - INFO - 初始化会话加载器
2025-08-08 23:47:53 - training_session_manager - INFO - 成功删除会话: 20250808_230919
2025-08-08 23:48:29 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-08 23:48:29 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.6313
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 14, 'min_samples_leaf': 9, 'criterion': 'entropy', 'class_weight': None, 'max_features': 'sqrt'}
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.6885
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/100
2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:48:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:48:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:48:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250808_234829.html
2025-08-08 23:48:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:48:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250808_234830.html
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.08 秒
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:48:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:48:34 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.7189
2025-08-08 23:48:35 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.7597
2025-08-08 23:48:36 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.8205
2025-08-08 23:48:38 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8346
2025-08-08 23:48:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8346
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 257, 'max_depth': 31, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': 'sqrt'}
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8346
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/100
2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:48:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:48:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:48:50 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_234850.html
2025-08-08 23:48:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:48:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_234850.html
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 20.96 秒
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.8759
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8796
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.8826
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8920
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8928
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8928
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8928
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8928
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.8982
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 242, 'max_depth': 7, 'learning_rate': 0.19980197193305507, 'subsample': 0.8517353574056075, 'colsample_bytree': 0.5154076893179076}
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8982
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/100
2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:48:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:48:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:48:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250808_234855.html
2025-08-08 23:48:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:48:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250808_234855.html
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.97 秒
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:48:56 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:49:02 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8573
2025-08-08 23:49:02 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.8784
2025-08-08 23:49:02 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 278, 'max_depth': 2, 'learning_rate': 0.1258847755362722, 'feature_fraction': 0.9687351890877102, 'bagging_fraction': 0.6429906519635253}
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.8914
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-08 23:49:03 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:49:03 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:49:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250808_234903.html
2025-08-08 23:49:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:49:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250808_234904.html
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.63 秒
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-08 23:49:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-08 23:49:09 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8051
2025-08-08 23:49:11 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8411
2025-08-08 23:49:20 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.8656
2025-08-08 23:49:24 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8846
2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8846
2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 247, 'depth': 3, 'learning_rate': 0.15912798713994736, 'l2_leaf_reg': 6.331731119758382, 'bagging_temperature': 0.046450412719997725}
2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.8846
2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/100
2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:50:21 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250808_235021.html
2025-08-08 23:50:21 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250808_235021.html
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 77.30 秒
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 2.3049175977973198, 'clf__solver': 'liblinear'}
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.7930
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 23:50:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:50:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250808_235022.html
2025-08-08 23:50:23 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:23 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250808_235023.html
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.27 秒
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 0.6077347352698871, 'clf__kernel': 'linear'}
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.8045
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 23:50:23 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:50:23 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250808_235023.html
2025-08-08 23:50:24 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250808_235024.html
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.28 秒
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:50:24 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.7520
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 3}
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.7526
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:50:25 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250808_235025.html
2025-08-08 23:50:25 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:50:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250808_235025.html
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.14 秒
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.7259
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 23:50:25 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-08 23:50:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8383
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8384
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8384
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8384
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 23:51:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8384
2025-08-08 23:51:07 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.8410
2025-08-08 23:51:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.0015148777761695774}
2025-08-08 23:51:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.8410
2025-08-08 23:51:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-08 23:51:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 23:51:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:51:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:51:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250808_235109.html
2025-08-08 23:51:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 23:51:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 23:51:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250808_235109.html
2025-08-08 23:51:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 44.31 秒
2025-08-08 23:51:26 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126
2025-08-08 23:51:26 - training_session_manager - INFO - 创建训练会话: 训练_nodule3_20250808_235126 (ID: 20250808_235126)
2025-08-08 23:51:26 - training_session_manager - INFO - 创建新会话: 训练_nodule3_20250808_235126
2025-08-08 23:51:26 - session_utils - INFO - 创建新会话: 训练_nodule3_20250808_235126 (ID: 20250808_235126)
2025-08-08 23:51:26 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-08 23:51:26 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-08 23:51:26 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8465
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.8245
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:26 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7500
2025-08-08 23:51:26 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\DecisionTree_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\DecisionTree_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.6500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8031
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.7200
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[19  4]
 [10  7]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.66      0.83      0.73        23
           1       0.64      0.41      0.50        17

    accuracy                           0.65        40
   macro avg       0.65      0.62      0.62        40
weighted avg       0.65      0.65      0.63        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 23:51:26 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.6500
2025-08-08 23:51:26 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\RandomForest_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\RandomForest_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:51:26 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:51:26 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8261
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.7850
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:51:26 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7500
2025-08-08 23:51:26 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\XGBoost_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\XGBoost_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:51:26 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:51:26 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8517
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.8381
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:51:27 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7500
2025-08-08 23:51:27 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\LightGBM_single_235127.joblib
2025-08-08 23:51:27 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\LightGBM_single_235127.joblib
2025-08-08 23:51:27 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:51:27 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:51:28 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.8593
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.8532
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 1.02 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-08 23:51:28 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\CatBoost_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\CatBoost_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.8159
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.8053
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[21  2]
 [ 7 10]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.91      0.82        23
           1       0.83      0.59      0.69        17

    accuracy                           0.78        40
   macro avg       0.79      0.75      0.76        40
weighted avg       0.79      0.78      0.77        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-08 23:51:28 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\Logistic_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\Logistic_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: SVM
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.6750
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.7980
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.7143
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[22  1]
 [12  5]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.96      0.77        23
           1       0.83      0.29      0.43        17

    accuracy                           0.68        40
   macro avg       0.74      0.63      0.60        40
weighted avg       0.73      0.68      0.63        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 SVM 性能: 准确率=0.6750
2025-08-08 23:51:28 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\SVM_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\SVM_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: KNN
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.6250
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.7647
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.6474
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[17  6]
 [ 9  8]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.74      0.69        23
           1       0.57      0.47      0.52        17

    accuracy                           0.62        40
   macro avg       0.61      0.60      0.61        40
weighted avg       0.62      0.62      0.62        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 KNN 性能: 准确率=0.6250
2025-08-08 23:51:28 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\KNN_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\KNN_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.6500
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.7724
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.7442
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[11 12]
 [ 2 15]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.48      0.61        23
           1       0.56      0.88      0.68        17

    accuracy                           0.65        40
   macro avg       0.70      0.68      0.65        40
weighted avg       0.72      0.65      0.64        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.6500
2025-08-08 23:51:28 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\NaiveBayes_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\NaiveBayes_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.8261
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.7876
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[19  4]
 [ 4 13]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.83      0.83        23
           1       0.76      0.76      0.76        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.56 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8000
2025-08-08 23:51:28 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\NeuralNet_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\NeuralNet_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
