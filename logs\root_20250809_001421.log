2025-08-09 00:14:22 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 00:14:23 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 00:14:23 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 00:14:23 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 00:14:23 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 00:14:24 - GUI - INFO - GUI界面初始化完成
2025-08-09 00:14:41 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 00:14:41 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9278
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9353
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9413
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9413
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 15, 'min_samples_leaf': 11, 'criterion': 'entropy', 'class_weight': None, 'max_features': 'sqrt'}
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9413
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:42 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_001442.html
2025-08-09 00:14:42 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_001442.html
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.89 秒
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9819
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 164, 'max_depth': 26, 'min_samples_split': 5, 'min_samples_leaf': 11, 'max_features': 'sqrt'}
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9827
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:48 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_001448.html
2025-08-09 00:14:48 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_001448.html
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.24 秒
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9869
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9884
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9900
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9908
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 138, 'max_depth': 8, 'learning_rate': 0.0928977494490788, 'subsample': 0.5168705837603152, 'colsample_bytree': 0.8432211211785746}
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9908
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_001453.html
2025-08-09 00:14:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_001454.html
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.37 秒
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9826
2025-08-09 00:14:58 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9867
2025-08-09 00:14:58 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9882
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'feature_fraction': 0.9744427686266666, 'bagging_fraction': 0.9828160165372797}
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9882
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:14:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_001459.html
2025-08-09 00:14:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:15:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_001459.html
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.85 秒
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:15:05 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9721
2025-08-09 00:15:06 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9820
2025-08-09 00:15:23 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 204, 'depth': 2, 'learning_rate': 0.11895186568849454, 'l2_leaf_reg': 8.448363213508443, 'bagging_temperature': 0.8095789183531145}
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9869
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:13 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_001613.html
2025-08-09 00:16:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_001614.html
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 74.32 秒
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9680
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9731
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.682586495876995, 'clf__solver': 'liblinear'}
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9740
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_001614.html
2025-08-09 00:16:15 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_001615.html
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.88 秒
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9867
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9884
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 6.026718993550662, 'clf__kernel': 'rbf'}
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:15 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_001615.html
2025-08-09 00:16:16 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_001616.html
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.18 秒
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9786
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9816
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:17 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_001617.html
2025-08-09 00:16:17 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_001617.html
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.05 秒
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9573
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:19 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9807
2025-08-09 00:16:20 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9857
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.008675143843171858}
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9857
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_001630.html
2025-08-09 00:16:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:16:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_001630.html
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.03 秒
2025-08-09 00:16:54 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654
2025-08-09 00:16:54 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250809_001654 (ID: 20250809_001654)
2025-08-09 00:16:54 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250809_001654
2025-08-09 00:16:54 - session_utils - INFO - 创建新会话: 训练_nodule2_20250809_001654 (ID: 20250809_001654)
2025-08-09 00:16:54 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 00:16:54 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:16:54 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.8951
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.7948
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-09 00:16:54 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\DecisionTree_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\DecisionTree_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.9412
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.9359
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 00:16:54 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\RandomForest_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\RandomForest_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:16:54 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:16:54 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.9000
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.9719
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.9627
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-09 00:16:54 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\XGBoost_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\XGBoost_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:16:54 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:16:54 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.9488
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.9492
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-09 00:16:54 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\LightGBM_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\LightGBM_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:16:54 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:16:55 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9591
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9570
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 1.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-09 00:16:55 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\CatBoost_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\CatBoost_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9284
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9288
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-09 00:16:55 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\Logistic_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\Logistic_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: SVM
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9182
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9034
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 00:16:55 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\SVM_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\SVM_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: KNN
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9322
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9189
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-09 00:16:55 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\KNN_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\KNN_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.8977
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9096
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-09 00:16:55 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\NaiveBayes_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\NaiveBayes_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:16:56 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:16:56 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:56 - model_training - INFO - AUC: 0.9591
2025-08-09 00:16:56 - model_training - INFO - AUPRC: 0.9450
2025-08-09 00:16:56 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:56 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:56 - model_training - INFO - 
分类报告:
2025-08-09 00:16:56 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:56 - model_training - INFO - 训练时间: 0.21 秒
2025-08-09 00:16:56 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 00:16:56 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\NeuralNet_single_001656.joblib
2025-08-09 00:16:56 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\NeuralNet_single_001656.joblib
2025-08-09 00:16:56 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
