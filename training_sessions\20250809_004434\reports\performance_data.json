{"generation_time": "2025-08-09T00:45:20.360180", "best_model": "LightGBM", "best_score": 0.8720257091619542, "model_count": 10, "detailed_metrics": {"KNN": {"accuracy": 0.8, "precision": 0.7368421052631579, "recall": 0.8235294117647058, "f1_score": 0.7777777777777778, "specificity": 0.782608695652174, "sensitivity": 0.8235294117647058, "npv": 0.8571428571428571, "ppv": 0.7368421052631579, "auc_roc": 0.9053708439897697, "auc_pr": 0.8759767064720625, "mcc": 0.6000307666669006, "kappa": 0.5969773299748111, "balanced_accuracy": 0.80306905370844, "composite_score": 0.7806900669077241}, "XGBoost": {"accuracy": 0.825, "precision": 0.75, "recall": 0.8823529411764706, "f1_score": 0.8108108108108109, "specificity": 0.782608695652174, "sensitivity": 0.8823529411764706, "npv": 0.9, "ppv": 0.75, "auc_roc": 0.9437340153452685, "auc_pr": 0.9386517765000737, "mcc": 0.6574382586514258, "kappa": 0.65, "balanced_accuracy": 0.8324808184143222, "composite_score": 0.8181276452054003}, "NaiveBayes": {"accuracy": 0.85, "precision": 0.8235294117647058, "recall": 0.8235294117647058, "f1_score": 0.8235294117647058, "specificity": 0.8695652173913043, "sensitivity": 0.8235294117647058, "npv": 0.8695652173913043, "ppv": 0.8235294117647058, "auc_roc": 0.9156010230179028, "auc_pr": 0.8954275481797791, "mcc": 0.6930946291560103, "kappa": 0.6930946291560103, "balanced_accuracy": 0.8465473145780051, "composite_score": 0.8263491048593351}, "DecisionTree": {"accuracy": 0.725, "precision": 0.6363636363636364, "recall": 0.8235294117647058, "f1_score": 0.717948717948718, "specificity": 0.6521739130434783, "sensitivity": 0.8235294117647058, "npv": 0.8333333333333334, "ppv": 0.6363636363636364, "auc_roc": 0.8209718670076727, "auc_pr": 0.6806529693216999, "mcc": 0.4726906072021924, "kappa": 0.458128078817734, "balanced_accuracy": 0.7378516624040921, "composite_score": 0.7064216652908583}, "RandomForest": {"accuracy": 0.85, "precision": 0.7894736842105263, "recall": 0.8823529411764706, "f1_score": 0.8333333333333334, "specificity": 0.8260869565217391, "sensitivity": 0.8823529411764706, "npv": 0.9047619047619048, "ppv": 0.7894736842105263, "auc_roc": 0.9501278772378516, "auc_pr": 0.9410319713489723, "mcc": 0.7013017821381075, "kappa": 0.6977329974811084, "balanced_accuracy": 0.8542199488491049, "composite_score": 0.8401615032430028}, "CatBoost": {"accuracy": 0.8, "precision": 0.7368421052631579, "recall": 0.8235294117647058, "f1_score": 0.7777777777777778, "specificity": 0.782608695652174, "sensitivity": 0.8235294117647058, "npv": 0.8571428571428571, "ppv": 0.7368421052631579, "auc_roc": 0.9539641943734014, "auc_pr": 0.9485903056874924, "mcc": 0.6000307666669006, "kappa": 0.5969773299748111, "balanced_accuracy": 0.80306905370844, "composite_score": 0.7904087369844506}, "NeuralNet": {"accuracy": 0.85, "precision": 0.7894736842105263, "recall": 0.8823529411764706, "f1_score": 0.8333333333333334, "specificity": 0.8260869565217391, "sensitivity": 0.8823529411764706, "npv": 0.9047619047619048, "ppv": 0.7894736842105263, "auc_roc": 0.9437340153452686, "auc_pr": 0.9159344020070664, "mcc": 0.7013017821381075, "kappa": 0.6977329974811084, "balanced_accuracy": 0.8542199488491049, "composite_score": 0.8388827308644862}, "LightGBM": {"accuracy": 0.875, "precision": 0.8, "recall": 0.9411764705882353, "f1_score": 0.8648648648648649, "specificity": 0.8260869565217391, "sensitivity": 0.9411764705882353, "npv": 0.95, "ppv": 0.8, "auc_roc": 0.9641943734015345, "auc_pr": 0.954140238211109, "mcc": 0.7585826061362605, "kappa": 0.75, "balanced_accuracy": 0.8836317135549872, "composite_score": 0.8720257091619542}, "SVM": {"accuracy": 0.825, "precision": 0.7272727272727273, "recall": 0.9411764705882353, "f1_score": 0.8205128205128205, "specificity": 0.7391304347826086, "sensitivity": 0.9411764705882353, "npv": 0.9444444444444444, "ppv": 0.7272727272727273, "auc_roc": 0.9258312020460358, "auc_pr": 0.9259006630394414, "mcc": 0.6759983952461461, "kappa": 0.6551724137931034, "balanced_accuracy": 0.840153452685422, "composite_score": 0.8246859434778375}, "Logistic": {"accuracy": 0.775, "precision": 0.7, "recall": 0.8235294117647058, "f1_score": 0.7567567567567568, "specificity": 0.7391304347826086, "sensitivity": 0.8235294117647058, "npv": 0.85, "ppv": 0.7, "auc_roc": 0.9028132992327366, "auc_pr": 0.8685132216462139, "mcc": 0.556293911166591, "kappa": 0.55, "balanced_accuracy": 0.7813299232736572, "composite_score": 0.7601375096375933}}, "ranking": [["LightGBM", 0.8720257091619542], ["RandomForest", 0.8401615032430028], ["NeuralNet", 0.8388827308644862], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.8263491048593351], ["SVM", 0.8246859434778375], ["XGBoost", 0.8181276452054003], ["CatBoost", 0.7904087369844506], ["KNN", 0.7806900669077241], ["Logistic", 0.7601375096375933], ["DecisionTree", 0.7064216652908583]]}