{"generation_time": "2025-08-09T00:55:04.093898", "best_model": "CatBoost", "best_score": 0.9169384817084134, "model_count": 10, "detailed_metrics": {"DecisionTree": {"accuracy": 0.875, "precision": 0.8333333333333334, "recall": 0.8823529411764706, "f1_score": 0.8571428571428571, "specificity": 0.8695652173913043, "sensitivity": 0.8823529411764706, "npv": 0.9090909090909091, "ppv": 0.8333333333333334, "auc_roc": 0.9309462915601023, "auc_pr": 0.8874649859943977, "mcc": 0.74715612106153, "kappa": 0.7461928934010152, "balanced_accuracy": 0.8759590792838874, "composite_score": 0.8582941890762918}, "CatBoost": {"accuracy": 0.925, "precision": 0.9375, "recall": 0.8823529411764706, "f1_score": 0.9090909090909091, "specificity": 0.9565217391304348, "sensitivity": 0.8823529411764706, "npv": 0.9166666666666666, "ppv": 0.9375, "auc_roc": 0.9820971867007673, "auc_pr": 0.9794943378420887, "mcc": 0.8464861424907173, "kappa": 0.845360824742268, "balanced_accuracy": 0.9194373401534527, "composite_score": 0.9169384817084134}, "NeuralNet": {"accuracy": 0.875, "precision": 0.8333333333333334, "recall": 0.8823529411764706, "f1_score": 0.8571428571428571, "specificity": 0.8695652173913043, "sensitivity": 0.8823529411764706, "npv": 0.9090909090909091, "ppv": 0.8333333333333334, "auc_roc": 0.9335038363171355, "auc_pr": 0.8779023078171686, "mcc": 0.74715612106153, "kappa": 0.7461928934010152, "balanced_accuracy": 0.8759590792838874, "composite_score": 0.8588056980276985}, "LightGBM": {"accuracy": 0.925, "precision": 0.9375, "recall": 0.8823529411764706, "f1_score": 0.9090909090909091, "specificity": 0.9565217391304348, "sensitivity": 0.8823529411764706, "npv": 0.9166666666666666, "ppv": 0.9375, "auc_roc": 0.9769820971867007, "auc_pr": 0.9664896954256816, "mcc": 0.8464861424907173, "kappa": 0.845360824742268, "balanced_accuracy": 0.9194373401534527, "composite_score": 0.9159154638056001}, "RandomForest": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9398976982097187, "auc_pr": 0.9432308732511573, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8551943398706895}, "SVM": {"accuracy": 0.8, "precision": 0.7142857142857143, "recall": 0.8823529411764706, "f1_score": 0.7894736842105263, "specificity": 0.7391304347826086, "sensitivity": 0.8823529411764706, "npv": 0.8947368421052632, "ppv": 0.7142857142857143, "auc_roc": 0.9002557544757033, "auc_pr": 0.9003408022803396, "mcc": 0.6152214189875816, "kappa": 0.6029776674937966, "balanced_accuracy": 0.8107416879795396, "composite_score": 0.789724898904711}, "KNN": {"accuracy": 0.825, "precision": 0.75, "recall": 0.8823529411764706, "f1_score": 0.8108108108108109, "specificity": 0.782608695652174, "sensitivity": 0.8823529411764706, "npv": 0.9, "ppv": 0.75, "auc_roc": 0.8746803069053709, "auc_pr": 0.7775331286360698, "mcc": 0.6574382586514258, "kappa": 0.65, "balanced_accuracy": 0.8324808184143222, "composite_score": 0.8043169035174208}, "Logistic": {"accuracy": 0.85, "precision": 0.7894736842105263, "recall": 0.8823529411764706, "f1_score": 0.8333333333333334, "specificity": 0.8260869565217391, "sensitivity": 0.8823529411764706, "npv": 0.9047619047619048, "ppv": 0.7894736842105263, "auc_roc": 0.9207161125319693, "auc_pr": 0.9287261328283, "mcc": 0.7013017821381075, "kappa": 0.6977329974811084, "balanced_accuracy": 0.8542199488491049, "composite_score": 0.8342791503018263}, "XGBoost": {"accuracy": 0.925, "precision": 0.9375, "recall": 0.8823529411764706, "f1_score": 0.9090909090909091, "specificity": 0.9565217391304348, "sensitivity": 0.8823529411764706, "npv": 0.9166666666666666, "ppv": 0.9375, "auc_roc": 0.9539641943734016, "auc_pr": 0.8766409845744211, "mcc": 0.8464861424907173, "kappa": 0.845360824742268, "balanced_accuracy": 0.9194373401534527, "composite_score": 0.9113118832429403}, "NaiveBayes": {"accuracy": 0.75, "precision": 0.7333333333333333, "recall": 0.6470588235294118, "f1_score": 0.6875, "specificity": 0.8260869565217391, "sensitivity": 0.6470588235294118, "npv": 0.76, "ppv": 0.7333333333333333, "auc_roc": 0.8554987212276215, "auc_pr": 0.7532965600828031, "mcc": 0.48313412715852977, "kappa": 0.48051948051948057, "balanced_accuracy": 0.7365728900255755, "composite_score": 0.7006286868487155}}, "ranking": [["CatBoost", 0.9169384817084134], ["LightGBM", 0.9159154638056001], ["XGBoost", 0.9113118832429403], ["NeuralNet", 0.8588056980276985], ["DecisionTree", 0.8582941890762918], ["RandomForest", 0.8551943398706895], ["Logistic", 0.8342791503018263], ["KNN", 0.8043169035174208], ["SVM", 0.789724898904711], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.7006286868487155]]}