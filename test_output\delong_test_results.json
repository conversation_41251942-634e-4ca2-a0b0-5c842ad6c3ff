{"pairwise_results": [{"model1": "RandomForest", "model2": "LogisticRegression", "auc1": 0.9927996799857771, "auc2": 0.9909773767723009, "z_score": 0.3308362365881275, "p_value": 0.7407681878704733, "is_significant": "False", "alpha": 0.05}, {"model1": "RandomForest", "model2": "SVM", "auc1": 0.9927996799857771, "auc2": 0.9963109471532068, "z_score": -0.906796122863339, "p_value": 0.3645146205692429, "is_significant": "False", "alpha": 0.05}, {"model1": "LogisticRegression", "model2": "SVM", "auc1": 0.9909773767723009, "auc2": 0.9963109471532068, "z_score": -1.1280499083856694, "p_value": 0.2592988422431659, "is_significant": "False", "alpha": 0.05}], "summary": {"total_comparisons": 3, "significant_comparisons": 0, "alpha": 0.05}}