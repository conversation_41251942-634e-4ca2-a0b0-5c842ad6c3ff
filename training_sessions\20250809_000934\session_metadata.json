{"session_id": "20250809_000934", "session_name": "训练_nodule3_20250809_000934", "description": "自动创建的训练会话，基于数据文件: nodule3", "created_time": "2025-08-09T00:09:34.542780", "last_modified": "2025-08-09T00:09:36.728307", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_000934.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\DecisionTree_single_000934.joblib", "save_time": "2025-08-09T00:09:34.569289"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_000934.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\RandomForest_single_000934.joblib", "save_time": "2025-08-09T00:09:34.680640"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_000934.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\XGBoost_single_000934.joblib", "save_time": "2025-08-09T00:09:34.798121"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_000934.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\LightGBM_single_000934.joblib", "save_time": "2025-08-09T00:09:34.892682"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_000936.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\CatBoost_single_000936.joblib", "save_time": "2025-08-09T00:09:36.005420"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_000936.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\Logistic_single_000936.joblib", "save_time": "2025-08-09T00:09:36.046552"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_000936.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\SVM_single_000936.joblib", "save_time": "2025-08-09T00:09:36.077809"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_000936.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\KNN_single_000936.joblib", "save_time": "2025-08-09T00:09:36.105318"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_000936.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\NaiveBayes_single_000936.joblib", "save_time": "2025-08-09T00:09:36.132943"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_000936.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_000934\\models\\NeuralNet_single_000936.joblib", "save_time": "2025-08-09T00:09:36.718939"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}