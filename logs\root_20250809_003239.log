2025-08-09 00:32:40 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 00:32:41 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 00:32:41 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 00:32:42 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 00:32:42 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 00:32:42 - GUI - INFO - GUI界面初始化完成
2025-08-09 00:33:06 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 4)
2025-08-09 00:33:06 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8791
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8814
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.8847
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 28: 发现更好的得分 0.9187
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 24, 'min_samples_leaf': 19, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9187
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/50
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:07 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_003307.html
2025-08-09 00:33:08 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_003308.html
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.48 秒
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:10 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9329
2025-08-09 00:33:13 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9351
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9359
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9359
2025-08-09 00:33:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9359
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - Trial 20: 发现更好的得分 0.9376
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 228, 'max_depth': 19, 'min_samples_split': 12, 'min_samples_leaf': 12, 'max_features': 'sqrt'}
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9376
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_003322.html
2025-08-09 00:33:23 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:23 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_003323.html
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.94 秒
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9205
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9227
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9246
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9261
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9320
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 183, 'max_depth': 3, 'learning_rate': 0.06535413272972584, 'subsample': 0.9363119836319117, 'colsample_bytree': 0.775626636539262}
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 35/50
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_003329.html
2025-08-09 00:33:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_003329.html
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.74 秒
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:36 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9150
2025-08-09 00:33:36 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9216
2025-08-09 00:33:36 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9324
2025-08-09 00:33:37 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9371
2025-08-09 00:33:37 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:37 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9379
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9379
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - Trial 22: 发现更好的得分 0.9386
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 108, 'max_depth': 4, 'learning_rate': 0.0109251166038683, 'feature_fraction': 0.8346109618081878, 'bagging_fraction': 0.6787756302293672}
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9386
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 实际执行试验次数: 28/50
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:38 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_003338.html
2025-08-09 00:33:38 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:33:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_003338.html
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.98 秒
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:33:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9152
2025-08-09 00:33:45 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9427
2025-08-09 00:34:02 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9451
2025-08-09 00:34:07 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9484
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9484
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 252, 'depth': 4, 'learning_rate': 0.03832491306185132, 'l2_leaf_reg': 7.158097238609412, 'bagging_temperature': 0.4401524937396013}
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9484
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_003450.html
2025-08-09 00:34:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_003450.html
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 72.12 秒
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9191
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9207
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9223
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.165984129783327, 'clf__solver': 'lbfgs'}
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 29/50
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_003452.html
2025-08-09 00:34:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_003452.html
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.73 秒
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9173
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9291
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 8.226265091680622, 'clf__kernel': 'rbf'}
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_003453.html
2025-08-09 00:34:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_003453.html
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.32 秒
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9044
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9220
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 7}
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 30/50
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_003455.html
2025-08-09 00:34:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:34:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_003455.html
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.62 秒
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9002
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:35:02 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9297
2025-08-09 00:35:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.006028306376486113}
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9297
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:35:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:35:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_003529.html
2025-08-09 00:35:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:35:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_003529.html
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 33.91 秒
2025-08-09 00:36:58 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658
2025-08-09 00:36:58 - training_session_manager - INFO - 创建训练会话: 训练_Vocs0807_20250809_003658 (ID: 20250809_003658)
2025-08-09 00:36:58 - training_session_manager - INFO - 创建新会话: 训练_Vocs0807_20250809_003658
2025-08-09 00:36:58 - session_utils - INFO - 创建新会话: 训练_Vocs0807_20250809_003658 (ID: 20250809_003658)
2025-08-09 00:36:58 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 4)
2025-08-09 00:36:58 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:36:58 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8593
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8251
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.00 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7750
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\DecisionTree_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\DecisionTree_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8696
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8887
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7500
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\RandomForest_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\RandomForest_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:36:58 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:36:58 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8849
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8789
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7750
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\XGBoost_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\XGBoost_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:36:58 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:36:58 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8645
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8724
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7500
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\LightGBM_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\LightGBM_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:36:58 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:36:59 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8747
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8870
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 1.07 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\CatBoost_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\CatBoost_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8389
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8490
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8000
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\Logistic_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\Logistic_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: SVM
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8440
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8150
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 SVM 性能: 准确率=0.8000
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\SVM_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\SVM_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: KNN
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8005
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.7075
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 KNN 性能: 准确率=0.7750
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\KNN_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\KNN_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8440
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8481
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.00 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8250
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NaiveBayes_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NaiveBayes_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8542
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8330
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.18 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.7750
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NeuralNet_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NeuralNet_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:39:21 - training_session_manager - INFO - 成功加载会话: 训练_nodule3_20250809_000934
2025-08-09 00:39:21 - session_loader - INFO - 初始化会话加载器
2025-08-09 00:39:26 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250809_001654
2025-08-09 00:39:26 - session_loader - INFO - 初始化会话加载器
2025-08-09 00:39:49 - training_session_manager - INFO - 成功加载会话: 训练_Vocs0807_20250809_003658
2025-08-09 00:39:49 - session_loader - INFO - 初始化会话加载器
