{"session_id": "20250809_002831", "session_name": "训练_Vocs250718_20250809_002831", "description": "自动创建的训练会话，基于数据文件: Vocs250718", "created_time": "2025-08-09T00:28:31.588202", "last_modified": "2025-08-09T00:28:33.674869", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_002831.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\DecisionTree_single_002831.joblib", "save_time": "2025-08-09T00:28:31.638696"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_002831.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\RandomForest_single_002831.joblib", "save_time": "2025-08-09T00:28:31.746237"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_002831.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\XGBoost_single_002831.joblib", "save_time": "2025-08-09T00:28:31.848655"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_002831.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\LightGBM_single_002831.joblib", "save_time": "2025-08-09T00:28:31.944912"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_002833.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\CatBoost_single_002833.joblib", "save_time": "2025-08-09T00:28:33.023929"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_002833.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\Logistic_single_002833.joblib", "save_time": "2025-08-09T00:28:33.062987"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_002833.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\SVM_single_002833.joblib", "save_time": "2025-08-09T00:28:33.091197"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_002833.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\KNN_single_002833.joblib", "save_time": "2025-08-09T00:28:33.118837"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_002833.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\NaiveBayes_single_002833.joblib", "save_time": "2025-08-09T00:28:33.150199"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_002833.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_002831\\models\\NeuralNet_single_002833.joblib", "save_time": "2025-08-09T00:28:33.661487"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}