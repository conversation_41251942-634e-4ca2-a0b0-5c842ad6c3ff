2025-08-09 01:28:28 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 01:28:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 01:28:29 - main - INFO - 已确保输出目录结构存在: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output
2025-08-09 01:28:29 - main - INFO - 已保存数据文件路径到缓存: sample_data.csv
2025-08-09 01:28:29 - main - INFO - 训练和优化 XGBoost
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1}
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8319
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8381
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.8436
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.8568
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8568
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 252, 'max_depth': 4, 'learning_rate': 0.03832491306185132, 'subsample': 0.8421165132560784, 'colsample_bytree': 0.7200762468698007}
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8568
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:28:34 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:28:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:28:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_012834.html
2025-08-09 01:28:35 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:28:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:28:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_012835.html
2025-08-09 01:28:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.81 秒
2025-08-09 01:28:35 - main - INFO - 使用最佳参数重新训练 XGBoost, 最佳分数: 0.8568
2025-08-09 01:28:35 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-09 01:28:35 - model_training - INFO - 模型名称: XGBoost
2025-08-09 01:28:35 - model_training - INFO - 准确率: 0.7500
2025-08-09 01:28:35 - model_training - INFO - AUC: 0.8325
2025-08-09 01:28:35 - model_training - INFO - AUPRC: 0.8542
2025-08-09 01:28:35 - model_training - INFO - 混淆矩阵:
2025-08-09 01:28:35 - model_training - INFO - 
[[15  5]
 [ 5 15]]
2025-08-09 01:28:35 - model_training - INFO - 
分类报告:
2025-08-09 01:28:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.75      0.75        20
           1       0.75      0.75      0.75        20

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 01:28:35 - model_training - INFO - 训练时间: 0.09 秒
2025-08-09 01:28:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7500
2025-08-09 01:28:35 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 01:28:35 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
