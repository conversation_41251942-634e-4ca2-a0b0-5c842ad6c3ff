2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1}
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8319
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:29 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8381
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.8436
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:30 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.8568
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8568
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 252, 'max_depth': 4, 'learning_rate': 0.03832491306185132, 'subsample': 0.8421165132560784, 'colsample_bytree': 0.7200762468698007}
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8568
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:28:34 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:28:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:28:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_012834.html
2025-08-09 01:28:35 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:28:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_012835.html
2025-08-09 01:28:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.81 秒
