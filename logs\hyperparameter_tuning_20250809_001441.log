2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9278
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9353
2025-08-09 00:14:41 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9413
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9413
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 15, 'min_samples_leaf': 11, 'criterion': 'entropy', 'class_weight': None, 'max_features': 'sqrt'}
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9413
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_001442.html
2025-08-09 00:14:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_001442.html
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.89 秒
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:42 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9819
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 164, 'max_depth': 26, 'min_samples_split': 5, 'min_samples_leaf': 11, 'max_features': 'sqrt'}
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9827
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_001448.html
2025-08-09 00:14:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_001448.html
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.24 秒
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9869
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9884
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9900
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:52 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9908
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 138, 'max_depth': 8, 'learning_rate': 0.0928977494490788, 'subsample': 0.5168705837603152, 'colsample_bytree': 0.8432211211785746}
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9908
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 00:14:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_001453.html
2025-08-09 00:14:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_001454.html
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.37 秒
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:14:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:14:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9826
2025-08-09 00:14:58 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9867
2025-08-09 00:14:58 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9882
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'feature_fraction': 0.9744427686266666, 'bagging_fraction': 0.9828160165372797}
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9882
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:14:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:14:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_001459.html
2025-08-09 00:15:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_001459.html
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.85 秒
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:15:00 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:15:05 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9721
2025-08-09 00:15:06 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9820
2025-08-09 00:15:23 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 204, 'depth': 2, 'learning_rate': 0.11895186568849454, 'l2_leaf_reg': 8.448363213508443, 'bagging_temperature': 0.8095789183531145}
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9869
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-09 00:16:13 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_001613.html
2025-08-09 00:16:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_001614.html
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 74.32 秒
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9680
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9731
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.682586495876995, 'clf__solver': 'liblinear'}
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9740
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-09 00:16:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_001614.html
2025-08-09 00:16:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_001615.html
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.88 秒
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9867
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9884
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 6.026718993550662, 'clf__kernel': 'rbf'}
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-09 00:16:15 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_001615.html
2025-08-09 00:16:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_001616.html
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.18 秒
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9786
2025-08-09 00:16:16 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9816
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_001617.html
2025-08-09 00:16:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_001617.html
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.05 秒
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9573
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:16:17 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:16:19 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9807
2025-08-09 00:16:20 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9857
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.008675143843171858}
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9857
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:16:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_001630.html
2025-08-09 00:16:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_001630.html
2025-08-09 00:16:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.03 秒
