2025-08-09 00:05:53 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 00:05:53 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 00:05:53 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 00:05:54 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 00:05:54 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 00:05:54 - GUI - INFO - GUI界面初始化完成
2025-08-09 00:06:29 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 00:06:29 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.6436
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.6969
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 28: 发现更好的得分 0.7387
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 9, 'min_samples_split': 37, 'min_samples_leaf': 21, 'criterion': 'entropy', 'class_weight': None, 'max_features': None}
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.7387
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 29/50
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:06:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:06:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_000629.html
2025-08-09 00:06:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:06:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_000630.html
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.36 秒
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:06:32 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.7026
2025-08-09 00:06:32 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.7100
2025-08-09 00:06:33 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8091
2025-08-09 00:06:33 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.8125
2025-08-09 00:06:38 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8436
2025-08-09 00:06:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 242, 'max_depth': 32, 'min_samples_split': 12, 'min_samples_leaf': 2, 'max_features': 'log2'}
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:06:48 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:06:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_000648.html
2025-08-09 00:06:48 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:06:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_000648.html
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.49 秒
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8915
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 56, 'max_depth': 4, 'learning_rate': 0.0964175830526111, 'subsample': 0.8323868634726811, 'colsample_bytree': 0.5093044318501301}
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:06:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:06:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_000652.html
2025-08-09 00:06:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:06:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_000652.html
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.04 秒
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:07:00 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8812
2025-08-09 00:07:00 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8919
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 268, 'max_depth': 4, 'learning_rate': 0.2511203898118411, 'feature_fraction': 0.8099857781688787, 'bagging_fraction': 0.6074770975788601}
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:07:02 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:07:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_000702.html
2025-08-09 00:07:02 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:07:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_000702.html
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.95 秒
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:07:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8051
2025-08-09 00:07:09 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8411
2025-08-09 00:07:17 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.8656
2025-08-09 00:07:22 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8846
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8846
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 247, 'depth': 3, 'learning_rate': 0.15912798713994736, 'l2_leaf_reg': 6.331731119758382, 'bagging_temperature': 0.046450412719997725}
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.8846
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:17 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_000817.html
2025-08-09 00:08:17 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_000817.html
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 74.89 秒
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 3.1148748233364714, 'clf__solver': 'liblinear'}
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:18 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_000818.html
2025-08-09 00:08:18 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_000818.html
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.11 秒
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.7536
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.7559
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.7930
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.041232043021419, 'clf__kernel': 'linear'}
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:19 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_000819.html
2025-08-09 00:08:20 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_000820.html
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.61 秒
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 3}
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:21 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_000821.html
2025-08-09 00:08:21 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:08:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_000821.html
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.36 秒
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.7259
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:37 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.8344
2025-08-09 00:08:37 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8408
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.009969290438929903}
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:09:03 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:09:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:09:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_000903.html
2025-08-09 00:09:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:09:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:09:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_000904.html
2025-08-09 00:09:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 42.59 秒
2025-08-09 00:09:34 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934
2025-08-09 00:09:34 - training_session_manager - INFO - 创建训练会话: 训练_nodule3_20250809_000934 (ID: 20250809_000934)
2025-08-09 00:09:34 - training_session_manager - INFO - 创建新会话: 训练_nodule3_20250809_000934
2025-08-09 00:09:34 - session_utils - INFO - 创建新会话: 训练_nodule3_20250809_000934 (ID: 20250809_000934)
2025-08-09 00:09:34 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 00:09:34 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:09:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8465
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.8245
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.00 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7500
2025-08-09 00:09:34 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\DecisionTree_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\DecisionTree_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.6500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8031
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.7200
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[19  4]
 [10  7]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.66      0.83      0.73        23
           1       0.64      0.41      0.50        17

    accuracy                           0.65        40
   macro avg       0.65      0.62      0.62        40
weighted avg       0.65      0.65      0.63        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.10 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.6500
2025-08-09 00:09:34 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\RandomForest_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\RandomForest_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:09:34 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:09:34 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8261
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.7850
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7500
2025-08-09 00:09:34 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\XGBoost_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\XGBoost_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:09:34 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:09:34 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8517
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.8381
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7500
2025-08-09 00:09:34 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\LightGBM_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\LightGBM_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:09:34 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:09:36 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.8593
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.8532
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 1.08 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-09 00:09:36 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\CatBoost_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\CatBoost_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.8159
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.8053
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[21  2]
 [ 7 10]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.91      0.82        23
           1       0.83      0.59      0.69        17

    accuracy                           0.78        40
   macro avg       0.79      0.75      0.76        40
weighted avg       0.79      0.78      0.77        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-09 00:09:36 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\Logistic_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\Logistic_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: SVM
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.6750
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.7980
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.7143
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[22  1]
 [12  5]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.96      0.77        23
           1       0.83      0.29      0.43        17

    accuracy                           0.68        40
   macro avg       0.74      0.63      0.60        40
weighted avg       0.73      0.68      0.63        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 SVM 性能: 准确率=0.6750
2025-08-09 00:09:36 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\SVM_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\SVM_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: KNN
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.6250
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.7647
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.6474
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[17  6]
 [ 9  8]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.74      0.69        23
           1       0.57      0.47      0.52        17

    accuracy                           0.62        40
   macro avg       0.61      0.60      0.61        40
weighted avg       0.62      0.62      0.62        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 KNN 性能: 准确率=0.6250
2025-08-09 00:09:36 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\KNN_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\KNN_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.6500
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.7724
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.7442
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[11 12]
 [ 2 15]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.48      0.61        23
           1       0.56      0.88      0.68        17

    accuracy                           0.65        40
   macro avg       0.70      0.68      0.65        40
weighted avg       0.72      0.65      0.64        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.6500
2025-08-09 00:09:36 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\NaiveBayes_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\NaiveBayes_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.8261
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.7876
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[19  4]
 [ 4 13]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.83      0.83        23
           1       0.76      0.76      0.76        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.80      0.80      0.80        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.56 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8000
2025-08-09 00:09:36 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\NeuralNet_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\NeuralNet_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:14:04 - training_session_manager - INFO - 成功加载会话: 训练_nodule3_20250808_235126
2025-08-09 00:14:04 - session_loader - INFO - 初始化会话加载器
2025-08-09 00:14:07 - training_session_manager - INFO - 成功删除会话: 20250808_235126
