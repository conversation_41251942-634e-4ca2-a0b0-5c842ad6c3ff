2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8791
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8814
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.8847
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9172
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - Trial 28: 发现更好的得分 0.9187
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 24, 'min_samples_leaf': 19, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9187
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/50
2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_003307.html
2025-08-09 00:33:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_003308.html
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.48 秒
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:08 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:10 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9329
2025-08-09 00:33:13 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9351
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9359
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9359
2025-08-09 00:33:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9359
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - Trial 20: 发现更好的得分 0.9376
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 228, 'max_depth': 19, 'min_samples_split': 12, 'min_samples_leaf': 12, 'max_features': 'sqrt'}
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9376
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_003322.html
2025-08-09 00:33:23 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_003323.html
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.94 秒
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9205
2025-08-09 00:33:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9227
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9246
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9261
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9320
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 183, 'max_depth': 3, 'learning_rate': 0.06535413272972584, 'subsample': 0.9363119836319117, 'colsample_bytree': 0.775626636539262}
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9320
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 35/50
2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_003329.html
2025-08-09 00:33:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_003329.html
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.74 秒
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:33:36 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9150
2025-08-09 00:33:36 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9216
2025-08-09 00:33:36 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9324
2025-08-09 00:33:37 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9371
2025-08-09 00:33:37 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:37 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9379
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9379
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - Trial 22: 发现更好的得分 0.9386
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 108, 'max_depth': 4, 'learning_rate': 0.0109251166038683, 'feature_fraction': 0.8346109618081878, 'bagging_fraction': 0.6787756302293672}
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9386
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 实际执行试验次数: 28/50
2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:33:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_003338.html
2025-08-09 00:33:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_003338.html
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 8.98 秒
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:33:39 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:33:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9152
2025-08-09 00:33:45 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9427
2025-08-09 00:34:02 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9451
2025-08-09 00:34:07 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9484
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9484
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 252, 'depth': 4, 'learning_rate': 0.03832491306185132, 'l2_leaf_reg': 7.158097238609412, 'bagging_temperature': 0.4401524937396013}
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9484
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:50 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_003450.html
2025-08-09 00:34:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_003450.html
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 72.12 秒
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9191
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9207
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9223
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.165984129783327, 'clf__solver': 'lbfgs'}
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9231
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 29/50
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_003452.html
2025-08-09 00:34:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_003452.html
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.73 秒
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:52 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9173
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9291
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 8.226265091680622, 'clf__kernel': 'rbf'}
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9307
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_003453.html
2025-08-09 00:34:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_003453.html
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.32 秒
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9044
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9220
2025-08-09 00:34:54 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 7}
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9244
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 30/50
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:34:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_003455.html
2025-08-09 00:34:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_003455.html
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.62 秒
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9002
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:34:55 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:35:02 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9297
2025-08-09 00:35:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9297
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.006028306376486113}
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9297
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:35:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_003529.html
2025-08-09 00:35:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_003529.html
2025-08-09 00:35:29 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 33.91 秒
