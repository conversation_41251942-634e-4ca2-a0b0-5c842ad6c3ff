2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8984
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9132
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9224
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 14: 发现更好的得分 0.9280
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.9515
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 5, 'min_samples_split': 13, 'min_samples_leaf': 5, 'criterion': 'entropy', 'class_weight': None, 'max_features': 'log2'}
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9524
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 实际执行试验次数: 35/100
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:51:46 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_005146.html
2025-08-09 00:51:46 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_005146.html
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.55 秒
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:51:49 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9280
2025-08-09 00:51:51 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9465
2025-08-09 00:51:57 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9490
2025-08-09 00:51:59 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9632
2025-08-09 00:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 75, 'max_depth': 18, 'min_samples_split': 9, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/100
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:52:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_005207.html
2025-08-09 00:52:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_005207.html
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.62 秒
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9824
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9849
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9883
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 278, 'max_depth': 8, 'learning_rate': 0.08116897882912005, 'subsample': 0.995707631782476, 'colsample_bytree': 0.7975094059918901}
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/100
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:52:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_005214.html
2025-08-09 00:52:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_005214.html
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 7.03 秒
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:52:22 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9893
2025-08-09 00:52:23 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9909
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9909
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9909
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 92, 'max_depth': 4, 'learning_rate': 0.2946045971142018, 'feature_fraction': 0.8564906369580745, 'bagging_fraction': 0.5002411648996792}
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 27/100
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:52:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_005224.html
2025-08-09 00:52:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_005224.html
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.47 秒
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:52:29 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9671
2025-08-09 00:52:31 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9749
2025-08-09 00:52:39 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9815
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9815
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/100
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_005324.html
2025-08-09 00:53:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_005325.html
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 60.58 秒
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9664
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9680
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 6.240874349180085, 'clf__solver': 'liblinear'}
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/100
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_005326.html
2025-08-09 00:53:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_005326.html
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.41 秒
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9785
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9826
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9826
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.9859
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 9.99020508064756, 'clf__kernel': 'rbf'}
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9868
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/100
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:27 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_005327.html
2025-08-09 00:53:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_005327.html
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.35 秒
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9228
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9276
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 7}
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_005328.html
2025-08-09 00:53:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_005329.html
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.31 秒
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.8995
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:39 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9795
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.0033209978031396824}
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_005353.html
2025-08-09 00:53:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_005353.html
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 24.35 秒
