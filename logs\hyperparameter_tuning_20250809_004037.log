2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9285
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9565
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9565
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9595
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.9629
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 16, 'min_samples_leaf': 5, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9629
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/100
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:40:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_004038.html
2025-08-09 00:40:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_004038.html
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.22 秒
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:40:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9826
2025-08-09 00:40:41 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9841
2025-08-09 00:40:43 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9858
2025-08-09 00:40:45 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9883
2025-08-09 00:40:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 198, 'max_depth': 19, 'min_samples_split': 14, 'min_samples_leaf': 9, 'max_features': 'log2'}
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/100
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:40:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_004059.html
2025-08-09 00:41:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_004059.html
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.18 秒
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9889
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9906
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9924
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9932
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9932
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 120, 'max_depth': 4, 'learning_rate': 0.10339125225715165, 'subsample': 0.5233649942281171, 'colsample_bytree': 0.949525715228648}
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:41:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_004104.html
2025-08-09 00:41:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_004104.html
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.57 秒
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:41:12 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9821
2025-08-09 00:41:12 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9855
2025-08-09 00:41:12 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 140, 'max_depth': 5, 'learning_rate': 0.016129853514510673, 'feature_fraction': 0.5904296538111249, 'bagging_fraction': 0.7471739158399349}
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:41:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_004113.html
2025-08-09 00:41:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_004114.html
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.57 秒
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:41:19 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9808
2025-08-09 00:41:20 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9841
2025-08-09 00:41:29 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9882
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9882
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/100
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_004209.html
2025-08-09 00:42:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_004209.html
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 55.18 秒
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9669
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.4618952310253128, 'clf__solver': 'lbfgs'}
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_004210.html
2025-08-09 00:42:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_004210.html
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.42 秒
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9779
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.3854830273850434, 'clf__kernel': 'linear'}
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_004211.html
2025-08-09 00:42:12 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_004212.html
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.29 秒
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:13 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9468
2025-08-09 00:42:13 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9592
2025-08-09 00:42:13 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 8}
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/100
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_004214.html
2025-08-09 00:42:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_004215.html
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.89 秒
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9525
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:22 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9667
2025-08-09 00:42:22 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9702
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.008577535844206527}
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_004233.html
2025-08-09 00:42:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_004233.html
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.36 秒
