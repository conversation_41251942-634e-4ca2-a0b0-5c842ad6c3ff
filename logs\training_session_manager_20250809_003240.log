2025-08-09 00:32:40 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 00:36:58 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658
2025-08-09 00:36:58 - training_session_manager - INFO - 创建训练会话: 训练_Vocs0807_20250809_003658 (ID: 20250809_003658)
2025-08-09 00:36:58 - training_session_manager - INFO - 创建新会话: 训练_Vocs0807_20250809_003658
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\DecisionTree_single_003658.joblib
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\RandomForest_single_003658.joblib
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\XGBoost_single_003658.joblib
2025-08-09 00:36:58 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\LightGBM_single_003658.joblib
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\CatBoost_single_003659.joblib
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\Logistic_single_003659.joblib
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\SVM_single_003659.joblib
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\KNN_single_003659.joblib
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NaiveBayes_single_003659.joblib
2025-08-09 00:36:59 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NeuralNet_single_003659.joblib
2025-08-09 00:39:21 - training_session_manager - INFO - 成功加载会话: 训练_nodule3_20250809_000934
2025-08-09 00:39:26 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250809_001654
2025-08-09 00:39:49 - training_session_manager - INFO - 成功加载会话: 训练_Vocs0807_20250809_003658
