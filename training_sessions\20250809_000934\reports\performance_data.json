{"generation_time": "2025-08-09T00:10:02.487193", "best_model": "NeuralNet", "best_score": 0.7561892583120204, "model_count": 10, "detailed_metrics": {"Logistic": {"accuracy": 0.775, "precision": 0.8333333333333334, "recall": 0.5882352941176471, "f1_score": 0.6896551724137931, "specificity": 0.9130434782608695, "sensitivity": 0.5882352941176471, "npv": 0.75, "ppv": 0.8333333333333334, "auc_roc": 0.8158567774936061, "auc_pr": 0.8053142019584341, "mcc": 0.5407518998772001, "kappa": 0.5212765957446809, "balanced_accuracy": 0.7506393861892583, "composite_score": 0.7117004690807069}, "KNN": {"accuracy": 0.625, "precision": 0.5714285714285714, "recall": 0.47058823529411764, "f1_score": 0.5161290322580645, "specificity": 0.7391304347826086, "sensitivity": 0.47058823529411764, "npv": 0.6538461538461539, "ppv": 0.5714285714285714, "auc_roc": 0.7647058823529411, "auc_pr": 0.647440794499618, "mcc": 0.21735757586639412, "kappa": 0.2146596858638744, "balanced_accuracy": 0.6048593350383631, "composite_score": 0.5388231403105637}, "NeuralNet": {"accuracy": 0.8, "precision": 0.7647058823529411, "recall": 0.7647058823529411, "f1_score": 0.7647058823529411, "specificity": 0.8260869565217391, "sensitivity": 0.7647058823529411, "npv": 0.8260869565217391, "ppv": 0.7647058823529411, "auc_roc": 0.826086956521739, "auc_pr": 0.7875984948555281, "mcc": 0.5907928388746803, "kappa": 0.5907928388746804, "balanced_accuracy": 0.7953964194373402, "composite_score": 0.7561892583120204}, "DecisionTree": {"accuracy": 0.75, "precision": 0.6842105263157895, "recall": 0.7647058823529411, "f1_score": 0.7222222222222222, "specificity": 0.7391304347826086, "sensitivity": 0.7647058823529411, "npv": 0.8095238095238095, "ppv": 0.6842105263157895, "auc_roc": 0.8465473145780051, "auc_pr": 0.8244608504670425, "mcc": 0.4987597511956937, "kappa": 0.49622166246851385, "balanced_accuracy": 0.7519181585677749, "composite_score": 0.7184053313397091}, "CatBoost": {"accuracy": 0.75, "precision": 0.7058823529411765, "recall": 0.7058823529411765, "f1_score": 0.7058823529411765, "specificity": 0.782608695652174, "sensitivity": 0.7058823529411765, "npv": 0.782608695652174, "ppv": 0.7058823529411765, "auc_roc": 0.8593350383631714, "auc_pr": 0.8531830798477907, "mcc": 0.4884910485933504, "kappa": 0.48849104859335035, "balanced_accuracy": 0.7442455242966752, "composite_score": 0.7105818414322251}, "RandomForest": {"accuracy": 0.65, "precision": 0.6363636363636364, "recall": 0.4117647058823529, "f1_score": 0.5, "specificity": 0.8260869565217391, "sensitivity": 0.4117647058823529, "npv": 0.6551724137931034, "ppv": 0.6363636363636364, "auc_roc": 0.80306905370844, "auc_pr": 0.7199838071184366, "mcc": 0.26332932647258134, "kappa": 0.24932975871313667, "balanced_accuracy": 0.618925831202046, "composite_score": 0.5548324610494736}, "NaiveBayes": {"accuracy": 0.65, "precision": 0.5555555555555556, "recall": 0.8823529411764706, "f1_score": 0.6818181818181818, "specificity": 0.4782608695652174, "sensitivity": 0.8823529411764706, "npv": 0.8461538461538461, "ppv": 0.5555555555555556, "auc_roc": 0.7723785166240409, "auc_pr": 0.7441811439334658, "mcc": 0.3806073543183196, "kappa": 0.334916864608076, "balanced_accuracy": 0.6803069053708439, "composite_score": 0.6611167173459964}, "LightGBM": {"accuracy": 0.75, "precision": 0.7058823529411765, "recall": 0.7058823529411765, "f1_score": 0.7058823529411765, "specificity": 0.782608695652174, "sensitivity": 0.7058823529411765, "npv": 0.782608695652174, "ppv": 0.7058823529411765, "auc_roc": 0.8516624040920716, "auc_pr": 0.8380591773780628, "mcc": 0.4884910485933504, "kappa": 0.48849104859335035, "balanced_accuracy": 0.7442455242966752, "composite_score": 0.7090473145780052}, "XGBoost": {"accuracy": 0.75, "precision": 0.7058823529411765, "recall": 0.7058823529411765, "f1_score": 0.7058823529411765, "specificity": 0.782608695652174, "sensitivity": 0.7058823529411765, "npv": 0.782608695652174, "ppv": 0.7058823529411765, "auc_roc": 0.8260869565217391, "auc_pr": 0.7849591146431116, "mcc": 0.4884910485933504, "kappa": 0.48849104859335035, "balanced_accuracy": 0.7442455242966752, "composite_score": 0.7039322250639387}, "SVM": {"accuracy": 0.675, "precision": 0.8333333333333334, "recall": 0.29411764705882354, "f1_score": 0.43478260869565216, "specificity": 0.9565217391304348, "sensitivity": 0.29411764705882354, "npv": 0.6470588235294118, "ppv": 0.8333333333333334, "auc_roc": 0.7979539641943735, "auc_pr": 0.7143094452256481, "mcc": 0.3469945177178053, "kappa": 0.2737430167597765, "balanced_accuracy": 0.6253196930946292, "composite_score": 0.5689641392944995}}, "ranking": [["NeuralNet", 0.7561892583120204], ["DecisionTree", 0.7184053313397091], ["Logistic", 0.7117004690807069], ["CatBoost", 0.7105818414322251], ["LightGBM", 0.7090473145780052], ["XGBoost", 0.7039322250639387], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.6611167173459964], ["SVM", 0.5689641392944995], ["RandomForest", 0.5548324610494736], ["KNN", 0.5388231403105637]]}