{"session_id": "20250809_005420", "session_name": "训练_N-3_20250809_005420", "description": "自动创建的训练会话，基于数据文件: N-3", "created_time": "2025-08-09T00:54:20.855575", "last_modified": "2025-08-09T00:54:22.630324", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_005420.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\DecisionTree_single_005420.joblib", "save_time": "2025-08-09T00:54:20.889800"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_005420.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\RandomForest_single_005420.joblib", "save_time": "2025-08-09T00:54:20.989351"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_005421.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\XGBoost_single_005421.joblib", "save_time": "2025-08-09T00:54:21.090719"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_005421.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\LightGBM_single_005421.joblib", "save_time": "2025-08-09T00:54:21.156438"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_005422.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\CatBoost_single_005422.joblib", "save_time": "2025-08-09T00:54:22.292995"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_005422.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\Logistic_single_005422.joblib", "save_time": "2025-08-09T00:54:22.318741"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_005422.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\SVM_single_005422.joblib", "save_time": "2025-08-09T00:54:22.343362"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_005422.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\KNN_single_005422.joblib", "save_time": "2025-08-09T00:54:22.367829"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_005422.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\NaiveBayes_single_005422.joblib", "save_time": "2025-08-09T00:54:22.390111"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_005422.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_005420\\models\\NeuralNet_single_005422.joblib", "save_time": "2025-08-09T00:54:22.623794"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}