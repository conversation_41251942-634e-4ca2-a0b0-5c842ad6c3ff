{"generation_time": "2025-08-09T00:29:57.480360", "best_model": "KNN", "best_score": 0.6823904788697523, "model_count": 10, "detailed_metrics": {"CatBoost": {"accuracy": 0.75, "precision": 0.6363636363636364, "recall": 0.5384615384615384, "f1_score": 0.5833333333333334, "specificity": 0.8518518518518519, "sensitivity": 0.5384615384615384, "npv": 0.7931034482758621, "ppv": 0.6363636363636364, "auc_roc": 0.7749287749287749, "auc_pr": 0.6143392555157261, "mcc": 0.40942246376285996, "kappa": 0.4065281899109793, "balanced_accuracy": 0.6951566951566952, "composite_score": 0.6217895674406269}, "RandomForest": {"accuracy": 0.75, "precision": 0.6363636363636364, "recall": 0.5384615384615384, "f1_score": 0.5833333333333334, "specificity": 0.8518518518518519, "sensitivity": 0.5384615384615384, "npv": 0.7931034482758621, "ppv": 0.6363636363636364, "auc_roc": 0.7763532763532764, "auc_pr": 0.6006766109707287, "mcc": 0.40942246376285996, "kappa": 0.4065281899109793, "balanced_accuracy": 0.6951566951566952, "composite_score": 0.6220744677255272}, "XGBoost": {"accuracy": 0.725, "precision": 0.6, "recall": 0.46153846153846156, "f1_score": 0.5217391304347826, "specificity": 0.8518518518518519, "sensitivity": 0.46153846153846156, "npv": 0.7666666666666667, "ppv": 0.6, "auc_roc": 0.7350427350427351, "auc_pr": 0.6248448610213316, "mcc": 0.3389834532487511, "kappa": 0.33333333333333337, "balanced_accuracy": 0.6566951566951567, "composite_score": 0.5701846603135854}, "KNN": {"accuracy": 0.775, "precision": 0.6428571428571429, "recall": 0.6923076923076923, "f1_score": 0.6666666666666666, "specificity": 0.8148148148148148, "sensitivity": 0.6923076923076923, "npv": 0.8461538461538461, "ppv": 0.6428571428571429, "auc_roc": 0.7891737891737892, "auc_pr": 0.6166103127641589, "mcc": 0.4979844161795724, "kappa": 0.4972067039106145, "balanced_accuracy": 0.7535612535612535, "composite_score": 0.6823904788697523}, "NaiveBayes": {"accuracy": 0.7, "precision": 0.5714285714285714, "recall": 0.3076923076923077, "f1_score": 0.4, "specificity": 0.8888888888888888, "sensitivity": 0.3076923076923077, "npv": 0.7272727272727273, "ppv": 0.5714285714285714, "auc_roc": 0.7492877492877493, "auc_pr": 0.6235149913499789, "mcc": 0.24232015747572203, "kappa": 0.22330097087378642, "balanced_accuracy": 0.5982905982905983, "composite_score": 0.50307370534704}, "Logistic": {"accuracy": 0.75, "precision": 0.6363636363636364, "recall": 0.5384615384615384, "f1_score": 0.5833333333333334, "specificity": 0.8518518518518519, "sensitivity": 0.5384615384615384, "npv": 0.7931034482758621, "ppv": 0.6363636363636364, "auc_roc": 0.7635327635327636, "auc_pr": 0.6500969231455658, "mcc": 0.40942246376285996, "kappa": 0.4065281899109793, "balanced_accuracy": 0.6951566951566952, "composite_score": 0.6195103651614247}, "NeuralNet": {"accuracy": 0.675, "precision": 0.5, "recall": 0.3076923076923077, "f1_score": 0.38095238095238093, "specificity": 0.8518518518518519, "sensitivity": 0.3076923076923077, "npv": 0.71875, "ppv": 0.5, "auc_roc": 0.7863247863247863, "auc_pr": 0.6671917163296474, "mcc": 0.18681617943926834, "kappa": 0.17721518987341778, "balanced_accuracy": 0.5797720797720798, "composite_score": 0.4838817065251699}, "DecisionTree": {"accuracy": 0.7, "precision": 0.5454545454545454, "recall": 0.46153846153846156, "f1_score": 0.5, "specificity": 0.8148148148148148, "sensitivity": 0.46153846153846156, "npv": 0.7586206896551724, "ppv": 0.5454545454545454, "auc_roc": 0.7193732193732194, "auc_pr": 0.5662448981414498, "mcc": 0.28988305828465266, "kappa": 0.28783382789317513, "balanced_accuracy": 0.6381766381766382, "composite_score": 0.5434060536662928}, "LightGBM": {"accuracy": 0.725, "precision": 0.6, "recall": 0.46153846153846156, "f1_score": 0.5217391304347826, "specificity": 0.8518518518518519, "sensitivity": 0.46153846153846156, "npv": 0.7666666666666667, "ppv": 0.6, "auc_roc": 0.7435897435897436, "auc_pr": 0.6714770469072089, "mcc": 0.3389834532487511, "kappa": 0.33333333333333337, "balanced_accuracy": 0.6566951566951567, "composite_score": 0.5718940620229871}, "SVM": {"accuracy": 0.775, "precision": 0.6666666666666666, "recall": 0.6153846153846154, "f1_score": 0.64, "specificity": 0.8518518518518519, "sensitivity": 0.6153846153846154, "npv": 0.8214285714285714, "ppv": 0.6666666666666666, "auc_roc": 0.7435897435897436, "auc_pr": 0.650484131253362, "mcc": 0.4775519811733183, "kappa": 0.4767441860465116, "balanced_accuracy": 0.7336182336182336, "composite_score": 0.6569084382016388}}, "ranking": [["KNN", 0.6823904788697523], ["SVM", 0.6569084382016388], ["RandomForest", 0.6220744677255272], ["CatBoost", 0.6217895674406269], ["Logistic", 0.6195103651614247], ["LightGBM", 0.5718940620229871], ["XGBoost", 0.5701846603135854], ["DecisionTree", 0.5434060536662928], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.50307370534704], ["NeuralNet", 0.4838817065251699]]}