{"session_id": "20250809_004434", "session_name": "训练_N-2_20250809_004434", "description": "自动创建的训练会话，基于数据文件: N-2", "created_time": "2025-08-09T00:44:34.765914", "last_modified": "2025-08-09T00:44:36.521287", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_004434.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\DecisionTree_single_004434.joblib", "save_time": "2025-08-09T00:44:34.808317"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_004434.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\RandomForest_single_004434.joblib", "save_time": "2025-08-09T00:44:34.907761"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_004435.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\XGBoost_single_004435.joblib", "save_time": "2025-08-09T00:44:35.017259"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_004435.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\LightGBM_single_004435.joblib", "save_time": "2025-08-09T00:44:35.087214"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_004436.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\CatBoost_single_004436.joblib", "save_time": "2025-08-09T00:44:36.245017"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_004436.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\Logistic_single_004436.joblib", "save_time": "2025-08-09T00:44:36.272080"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_004436.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\SVM_single_004436.joblib", "save_time": "2025-08-09T00:44:36.297236"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_004436.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\KNN_single_004436.joblib", "save_time": "2025-08-09T00:44:36.332358"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_004436.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\NaiveBayes_single_004436.joblib", "save_time": "2025-08-09T00:44:36.353058"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_004436.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_004434\\models\\NeuralNet_single_004436.joblib", "save_time": "2025-08-09T00:44:36.517287"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}