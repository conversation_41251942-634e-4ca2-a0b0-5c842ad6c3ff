2025-08-09 00:40:14 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 00:40:14 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 00:40:14 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 00:40:15 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 00:40:15 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 00:40:15 - GUI - INFO - GUI界面初始化完成
2025-08-09 00:40:37 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-09 00:40:37 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:40:37 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9285
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9565
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9565
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9595
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.9629
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 16, 'min_samples_leaf': 5, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9629
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/100
2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:40:38 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:40:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:40:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_004038.html
2025-08-09 00:40:38 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:40:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_004038.html
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.22 秒
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:40:39 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:40:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9826
2025-08-09 00:40:41 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9841
2025-08-09 00:40:43 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9858
2025-08-09 00:40:45 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9883
2025-08-09 00:40:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 198, 'max_depth': 19, 'min_samples_split': 14, 'min_samples_leaf': 9, 'max_features': 'log2'}
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/100
2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:40:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:40:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:40:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_004059.html
2025-08-09 00:40:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:41:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_004059.html
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.18 秒
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9889
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9906
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9924
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9932
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9932
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 120, 'max_depth': 4, 'learning_rate': 0.10339125225715165, 'subsample': 0.5233649942281171, 'colsample_bytree': 0.949525715228648}
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9933
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:41:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:41:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_004104.html
2025-08-09 00:41:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:41:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_004104.html
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.57 秒
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:41:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:41:12 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9821
2025-08-09 00:41:12 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9855
2025-08-09 00:41:12 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 140, 'max_depth': 5, 'learning_rate': 0.016129853514510673, 'feature_fraction': 0.5904296538111249, 'bagging_fraction': 0.7471739158399349}
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9873
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-09 00:41:13 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:41:13 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:41:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_004113.html
2025-08-09 00:41:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:41:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_004114.html
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.57 秒
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:41:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:41:19 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9808
2025-08-09 00:41:20 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9841
2025-08-09 00:41:29 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9882
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9882
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/100
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_004209.html
2025-08-09 00:42:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_004209.html
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 55.18 秒
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9669
2025-08-09 00:42:09 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.4618952310253128, 'clf__solver': 'lbfgs'}
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9686
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/100
2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:10 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_004210.html
2025-08-09 00:42:10 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_004210.html
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.42 秒
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9779
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.3854830273850434, 'clf__kernel': 'linear'}
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9787
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:11 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_004211.html
2025-08-09 00:42:12 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:12 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_004212.html
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.29 秒
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:12 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:13 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9468
2025-08-09 00:42:13 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9592
2025-08-09 00:42:13 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 8}
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9622
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/100
2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_004214.html
2025-08-09 00:42:15 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_004215.html
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.89 秒
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9525
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:42:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:42:22 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9667
2025-08-09 00:42:22 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9702
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.008577535844206527}
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9702
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:42:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_004233.html
2025-08-09 00:42:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:42:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_004233.html
2025-08-09 00:42:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.36 秒
2025-08-09 00:44:34 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434
2025-08-09 00:44:34 - training_session_manager - INFO - 创建训练会话: 训练_N-2_20250809_004434 (ID: 20250809_004434)
2025-08-09 00:44:34 - training_session_manager - INFO - 创建新会话: 训练_N-2_20250809_004434
2025-08-09 00:44:34 - session_utils - INFO - 创建新会话: 训练_N-2_20250809_004434 (ID: 20250809_004434)
2025-08-09 00:44:34 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-09 00:44:34 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:44:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:44:34 - model_training - INFO - 准确率: 0.7250
2025-08-09 00:44:34 - model_training - INFO - AUC: 0.8210
2025-08-09 00:44:34 - model_training - INFO - AUPRC: 0.6807
2025-08-09 00:44:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:34 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-09 00:44:34 - model_training - INFO - 
分类报告:
2025-08-09 00:44:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-09 00:44:34 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-09 00:44:34 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\DecisionTree_single_004434.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\DecisionTree_single_004434.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:44:34 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:44:34 - model_training - INFO - AUC: 0.9501
2025-08-09 00:44:34 - model_training - INFO - AUPRC: 0.9410
2025-08-09 00:44:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:34 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-09 00:44:34 - model_training - INFO - 
分类报告:
2025-08-09 00:44:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-09 00:44:34 - model_training - INFO - 训练时间: 0.07 秒
2025-08-09 00:44:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-09 00:44:34 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\RandomForest_single_004434.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\RandomForest_single_004434.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:44:34 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:44:34 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:44:34 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:44:34 - model_training - INFO - AUC: 0.9437
2025-08-09 00:44:34 - model_training - INFO - AUPRC: 0.9387
2025-08-09 00:44:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:34 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-09 00:44:34 - model_training - INFO - 
分类报告:
2025-08-09 00:44:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-09 00:44:35 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:44:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-09 00:44:35 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\XGBoost_single_004435.joblib
2025-08-09 00:44:35 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\XGBoost_single_004435.joblib
2025-08-09 00:44:35 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:44:35 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:44:35 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:44:35 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:44:35 - model_training - INFO - AUC: 0.9642
2025-08-09 00:44:35 - model_training - INFO - AUPRC: 0.9541
2025-08-09 00:44:35 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:35 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-09 00:44:35 - model_training - INFO - 
分类报告:
2025-08-09 00:44:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-09 00:44:35 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:44:35 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-09 00:44:35 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\LightGBM_single_004435.joblib
2025-08-09 00:44:35 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\LightGBM_single_004435.joblib
2025-08-09 00:44:35 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:44:35 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:44:36 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9540
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.9486
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 1.12 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-09 00:44:36 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\CatBoost_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\CatBoost_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9028
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.8685
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-09 00:44:36 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\Logistic_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\Logistic_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: SVM
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9258
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.9259
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-09 00:44:36 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\SVM_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\SVM_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: KNN
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9054
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.8760
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 KNN 性能: 准确率=0.8000
2025-08-09 00:44:36 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\KNN_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\KNN_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9156
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.8954
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-09 00:44:36 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\NaiveBayes_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\NaiveBayes_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9437
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.9159
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.15 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-09 00:44:36 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\NeuralNet_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\NeuralNet_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
