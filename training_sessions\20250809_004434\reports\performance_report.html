
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模型性能比较报告</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #34495e;
                margin-top: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f2f2f2;
            }
            .best-score {
                background-color: #2ecc71 !important;
                color: white;
                font-weight: bold;
            }
            .summary {
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }
            .metric-description {
                font-size: 0.9em;
                color: #7f8c8d;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: 2025-08-09 00:45:20</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型:</strong> LightGBM (综合得分: 0.872)</p>
                <p><strong>比较模型数量:</strong> 10</p>
                <p><strong>评估指标数量:</strong> 13</p>
            </div>
            
            <h2>🏆 模型排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                </tr>
    
                <tr>
                    <td>1</td>
                    <td><strong>LightGBM</strong></td>
                    <td class="best-score">0.872</td>
                    <td>0.875</td>
                    <td>0.800</td>
                    <td>0.941</td>
                    <td>0.865</td>
                    <td>0.964</td>
                </tr>
        
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.840</td>
                    <td>0.850</td>
                    <td>0.789</td>
                    <td>0.882</td>
                    <td>0.833</td>
                    <td>0.950</td>
                </tr>
        
                <tr>
                    <td>3</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="">0.839</td>
                    <td>0.850</td>
                    <td>0.789</td>
                    <td>0.882</td>
                    <td>0.833</td>
                    <td>0.944</td>
                </tr>
        
                <tr>
                    <td>4</td>
                    <td><strong>NaiveBayes</strong></td>
                    <td class="">0.826</td>
                    <td>0.850</td>
                    <td>0.824</td>
                    <td>0.824</td>
                    <td>0.824</td>
                    <td>0.916</td>
                </tr>
        
                <tr>
                    <td>5</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.825</td>
                    <td>0.825</td>
                    <td>0.727</td>
                    <td>0.941</td>
                    <td>0.821</td>
                    <td>0.926</td>
                </tr>
        
                <tr>
                    <td>6</td>
                    <td><strong>XGBoost</strong></td>
                    <td class="">0.818</td>
                    <td>0.825</td>
                    <td>0.750</td>
                    <td>0.882</td>
                    <td>0.811</td>
                    <td>0.944</td>
                </tr>
        
                <tr>
                    <td>7</td>
                    <td><strong>CatBoost</strong></td>
                    <td class="">0.790</td>
                    <td>0.800</td>
                    <td>0.737</td>
                    <td>0.824</td>
                    <td>0.778</td>
                    <td>0.954</td>
                </tr>
        
                <tr>
                    <td>8</td>
                    <td><strong>KNN</strong></td>
                    <td class="">0.781</td>
                    <td>0.800</td>
                    <td>0.737</td>
                    <td>0.824</td>
                    <td>0.778</td>
                    <td>0.905</td>
                </tr>
        
                <tr>
                    <td>9</td>
                    <td><strong>Logistic</strong></td>
                    <td class="">0.760</td>
                    <td>0.775</td>
                    <td>0.700</td>
                    <td>0.824</td>
                    <td>0.757</td>
                    <td>0.903</td>
                </tr>
        
                <tr>
                    <td>10</td>
                    <td><strong>DecisionTree</strong></td>
                    <td class="">0.706</td>
                    <td>0.725</td>
                    <td>0.636</td>
                    <td>0.824</td>
                    <td>0.718</td>
                    <td>0.821</td>
                </tr>
        
            </table>
            
            <h2>📈 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    <th>准确率</th><th>精确率</th><th>召回率</th><th>F1分数</th><th>特异性</th><th>敏感性</th><th>阴性预测值</th><th>阳性预测值</th><th>AUC-ROC</th><th>AUC-PR</th><th>MCC</th><th>Kappa</th><th>平衡准确率</th></tr><tr><td><strong>KNN</strong></td><td class="">0.800</td><td class="">0.737</td><td class="">0.824</td><td class="">0.778</td><td class="">0.783</td><td class="">0.824</td><td class="">0.857</td><td class="">0.737</td><td class="">0.905</td><td class="">0.876</td><td class="">0.600</td><td class="">0.597</td><td class="">0.803</td></tr><tr><td><strong>XGBoost</strong></td><td class="">0.825</td><td class="">0.750</td><td class="">0.882</td><td class="">0.811</td><td class="">0.783</td><td class="">0.882</td><td class="">0.900</td><td class="">0.750</td><td class="">0.944</td><td class="">0.939</td><td class="">0.657</td><td class="">0.650</td><td class="">0.832</td></tr><tr><td><strong>NaiveBayes</strong></td><td class="">0.850</td><td class="best-score">0.824</td><td class="">0.824</td><td class="">0.824</td><td class="best-score">0.870</td><td class="">0.824</td><td class="">0.870</td><td class="best-score">0.824</td><td class="">0.916</td><td class="">0.895</td><td class="">0.693</td><td class="">0.693</td><td class="">0.847</td></tr><tr><td><strong>DecisionTree</strong></td><td class="">0.725</td><td class="">0.636</td><td class="">0.824</td><td class="">0.718</td><td class="">0.652</td><td class="">0.824</td><td class="">0.833</td><td class="">0.636</td><td class="">0.821</td><td class="">0.681</td><td class="">0.473</td><td class="">0.458</td><td class="">0.738</td></tr><tr><td><strong>RandomForest</strong></td><td class="">0.850</td><td class="">0.789</td><td class="">0.882</td><td class="">0.833</td><td class="">0.826</td><td class="">0.882</td><td class="">0.905</td><td class="">0.789</td><td class="">0.950</td><td class="">0.941</td><td class="">0.701</td><td class="">0.698</td><td class="">0.854</td></tr><tr><td><strong>CatBoost</strong></td><td class="">0.800</td><td class="">0.737</td><td class="">0.824</td><td class="">0.778</td><td class="">0.783</td><td class="">0.824</td><td class="">0.857</td><td class="">0.737</td><td class="">0.954</td><td class="">0.949</td><td class="">0.600</td><td class="">0.597</td><td class="">0.803</td></tr><tr><td><strong>NeuralNet</strong></td><td class="">0.850</td><td class="">0.789</td><td class="">0.882</td><td class="">0.833</td><td class="">0.826</td><td class="">0.882</td><td class="">0.905</td><td class="">0.789</td><td class="">0.944</td><td class="">0.916</td><td class="">0.701</td><td class="">0.698</td><td class="">0.854</td></tr><tr><td><strong>LightGBM</strong></td><td class="best-score">0.875</td><td class="">0.800</td><td class="best-score">0.941</td><td class="best-score">0.865</td><td class="">0.826</td><td class="best-score">0.941</td><td class="best-score">0.950</td><td class="">0.800</td><td class="best-score">0.964</td><td class="best-score">0.954</td><td class="best-score">0.759</td><td class="best-score">0.750</td><td class="best-score">0.884</td></tr><tr><td><strong>SVM</strong></td><td class="">0.825</td><td class="">0.727</td><td class="best-score">0.941</td><td class="">0.821</td><td class="">0.739</td><td class="best-score">0.941</td><td class="">0.944</td><td class="">0.727</td><td class="">0.926</td><td class="">0.926</td><td class="">0.676</td><td class="">0.655</td><td class="">0.840</td></tr><tr><td><strong>Logistic</strong></td><td class="">0.775</td><td class="">0.700</td><td class="">0.824</td><td class="">0.757</td><td class="">0.739</td><td class="">0.824</td><td class="">0.850</td><td class="">0.700</td><td class="">0.903</td><td class="">0.869</td><td class="">0.556</td><td class="">0.550</td><td class="">0.781</td></tr>
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC-ROC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于综合性能评估，我们推荐使用 <strong>LightGBM</strong> 模型进行后续任务。</p>
                <p>如果您对特定指标有具体要求，请参考详细性能指标表格选择最合适的模型。</p>
            </div>
        </div>
    </body>
    </html>
    