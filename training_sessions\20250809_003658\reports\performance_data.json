{"generation_time": "2025-08-09T00:37:36.450145", "best_model": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "best_score": 0.7827085100534257, "model_count": 10, "detailed_metrics": {"KNN": {"accuracy": 0.775, "precision": 0.7, "recall": 0.8235294117647058, "f1_score": 0.7567567567567568, "specificity": 0.7391304347826086, "sensitivity": 0.8235294117647058, "npv": 0.85, "ppv": 0.7, "auc_roc": 0.8005115089514067, "auc_pr": 0.7075163398692811, "mcc": 0.556293911166591, "kappa": 0.55, "balanced_accuracy": 0.7813299232736572, "composite_score": 0.7396771515813272}, "Logistic": {"accuracy": 0.8, "precision": 0.7368421052631579, "recall": 0.8235294117647058, "f1_score": 0.7777777777777778, "specificity": 0.782608695652174, "sensitivity": 0.8235294117647058, "npv": 0.8571428571428571, "ppv": 0.7368421052631579, "auc_roc": 0.8388746803069054, "auc_pr": 0.8490151705954252, "mcc": 0.6000307666669006, "kappa": 0.5969773299748111, "balanced_accuracy": 0.80306905370844, "composite_score": 0.7673908341711513}, "NeuralNet": {"accuracy": 0.775, "precision": 0.7, "recall": 0.8235294117647058, "f1_score": 0.7567567567567568, "specificity": 0.7391304347826086, "sensitivity": 0.8235294117647058, "npv": 0.85, "ppv": 0.7, "auc_roc": 0.8542199488491048, "auc_pr": 0.8329805189572754, "mcc": 0.556293911166591, "kappa": 0.55, "balanced_accuracy": 0.7813299232736572, "composite_score": 0.7504188395608669}, "XGBoost": {"accuracy": 0.775, "precision": 0.7222222222222222, "recall": 0.7647058823529411, "f1_score": 0.7428571428571429, "specificity": 0.782608695652174, "sensitivity": 0.7647058823529411, "npv": 0.8181818181818182, "ppv": 0.7222222222222222, "auc_roc": 0.8849104859335039, "auc_pr": 0.8789391035521067, "mcc": 0.5438483330175762, "kappa": 0.5431472081218274, "balanced_accuracy": 0.7736572890025575, "composite_score": 0.7464199913970402}, "CatBoost": {"accuracy": 0.75, "precision": 0.6842105263157895, "recall": 0.7647058823529411, "f1_score": 0.7222222222222222, "specificity": 0.7391304347826086, "sensitivity": 0.7647058823529411, "npv": 0.8095238095238095, "ppv": 0.6842105263157895, "auc_roc": 0.8746803069053708, "auc_pr": 0.8870461910254298, "mcc": 0.4987597511956937, "kappa": 0.49622166246851385, "balanced_accuracy": 0.7519181585677749, "composite_score": 0.7240319298051823}, "SVM": {"accuracy": 0.8, "precision": 0.7368421052631579, "recall": 0.8235294117647058, "f1_score": 0.7777777777777778, "specificity": 0.782608695652174, "sensitivity": 0.8235294117647058, "npv": 0.8571428571428571, "ppv": 0.7368421052631579, "auc_roc": 0.8439897698209718, "auc_pr": 0.8150095188167847, "mcc": 0.6000307666669006, "kappa": 0.5969773299748111, "balanced_accuracy": 0.80306905370844, "composite_score": 0.7684138520739646}, "NaiveBayes": {"accuracy": 0.825, "precision": 0.8125, "recall": 0.7647058823529411, "f1_score": 0.7878787878787878, "specificity": 0.8695652173913043, "sensitivity": 0.7647058823529411, "npv": 0.8333333333333334, "ppv": 0.8125, "auc_roc": 0.8439897698209718, "auc_pr": 0.8481457268221975, "mcc": 0.6400261077368838, "kappa": 0.6391752577319587, "balanced_accuracy": 0.8171355498721227, "composite_score": 0.7827085100534257}, "DecisionTree": {"accuracy": 0.775, "precision": 0.7222222222222222, "recall": 0.7647058823529411, "f1_score": 0.7428571428571429, "specificity": 0.782608695652174, "sensitivity": 0.7647058823529411, "npv": 0.8181818181818182, "ppv": 0.7222222222222222, "auc_roc": 0.8593350383631713, "auc_pr": 0.8250511433574315, "mcc": 0.5438483330175762, "kappa": 0.5431472081218274, "balanced_accuracy": 0.7736572890025575, "composite_score": 0.7413049018829737}, "LightGBM": {"accuracy": 0.75, "precision": 0.6842105263157895, "recall": 0.7647058823529411, "f1_score": 0.7222222222222222, "specificity": 0.7391304347826086, "sensitivity": 0.7647058823529411, "npv": 0.8095238095238095, "ppv": 0.6842105263157895, "auc_roc": 0.8644501278772377, "auc_pr": 0.8723520760285467, "mcc": 0.4987597511956937, "kappa": 0.49622166246851385, "balanced_accuracy": 0.7519181585677749, "composite_score": 0.7219858939995556}, "RandomForest": {"accuracy": 0.75, "precision": 0.6842105263157895, "recall": 0.7647058823529411, "f1_score": 0.7222222222222222, "specificity": 0.7391304347826086, "sensitivity": 0.7647058823529411, "npv": 0.8095238095238095, "ppv": 0.6842105263157895, "auc_roc": 0.8695652173913044, "auc_pr": 0.8887174100409394, "mcc": 0.4987597511956937, "kappa": 0.49622166246851385, "balanced_accuracy": 0.7519181585677749, "composite_score": 0.7230089119023689}}, "ranking": [["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.7827085100534257], ["SVM", 0.7684138520739646], ["Logistic", 0.7673908341711513], ["NeuralNet", 0.7504188395608669], ["XGBoost", 0.7464199913970402], ["DecisionTree", 0.7413049018829737], ["KNN", 0.7396771515813272], ["CatBoost", 0.7240319298051823], ["RandomForest", 0.7230089119023689], ["LightGBM", 0.7219858939995556]]}