{"session_id": "20250809_003658", "session_name": "训练_Vocs0807_20250809_003658", "description": "自动创建的训练会话，基于数据文件: Vocs0807", "created_time": "2025-08-09T00:36:58.057228", "last_modified": "2025-08-09T00:36:59.805980", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_003658.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\DecisionTree_single_003658.joblib", "save_time": "2025-08-09T00:36:58.104348"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_003658.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\RandomForest_single_003658.joblib", "save_time": "2025-08-09T00:36:58.198904"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_003658.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\XGBoost_single_003658.joblib", "save_time": "2025-08-09T00:36:58.311238"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_003658.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\LightGBM_single_003658.joblib", "save_time": "2025-08-09T00:36:58.382306"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_003659.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\CatBoost_single_003659.joblib", "save_time": "2025-08-09T00:36:59.472438"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_003659.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\Logistic_single_003659.joblib", "save_time": "2025-08-09T00:36:59.504014"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_003659.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\SVM_single_003659.joblib", "save_time": "2025-08-09T00:36:59.536156"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_003659.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\KNN_single_003659.joblib", "save_time": "2025-08-09T00:36:59.567522"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_003659.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\NaiveBayes_single_003659.joblib", "save_time": "2025-08-09T00:36:59.599534"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_003659.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_003658\\models\\NeuralNet_single_003659.joblib", "save_time": "2025-08-09T00:36:59.797844"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}