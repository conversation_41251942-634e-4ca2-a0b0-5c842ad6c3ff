2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.7994
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8094
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8094
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8094
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.8177
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8094
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 28, 'min_samples_leaf': 13, 'criterion': 'gini', 'class_weight': None, 'max_features': 'log2'}
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.8177
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:23:41 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:23:41 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_002341.html
2025-08-09 00:23:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_002341.html
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.07 秒
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:23:42 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:23:45 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8539
2025-08-09 00:23:46 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8560
2025-08-09 00:23:48 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.8650
2025-08-09 00:23:49 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.8668
2025-08-09 00:23:53 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.8718
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8718
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 55, 'max_depth': 30, 'min_samples_split': 9, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8718
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/50
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:23:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_002359.html
2025-08-09 00:23:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_002359.html
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 17.62 秒
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:23:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:00 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8205
2025-08-09 00:24:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:00 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.8313
2025-08-09 00:24:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:00 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8382
2025-08-09 00:24:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.8465
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8479
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.8605
2025-08-09 00:24:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8605
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8605
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8605
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8605
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8605
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - Trial 24: 发现更好的得分 0.8617
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - Trial 25: 发现更好的得分 0.8642
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 100, 'max_depth': 4, 'learning_rate': 0.051036213397902944, 'subsample': 0.8966130161809198, 'colsample_bytree': 0.6295622134160329}
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8642
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/50
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:24:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_002404.html
2025-08-09 00:24:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_002404.html
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.22 秒
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:24:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:24:11 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8481
2025-08-09 00:24:12 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.8524
2025-08-09 00:24:13 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8611
2025-08-09 00:24:13 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.8668
2025-08-09 00:24:13 - hyperparameter_tuning - INFO - Trial 20: 发现更好的得分 0.8699
2025-08-09 00:24:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 242, 'max_depth': 2, 'learning_rate': 0.01731677088999279, 'feature_fraction': 0.8053649983437834, 'bagging_fraction': 0.805497817593243}
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.8699
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 实际执行试验次数: 37/50
2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:24:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:24:15 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_002415.html
2025-08-09 00:24:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_002415.html
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.12 秒
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:24:16 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:24:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8193
2025-08-09 00:24:22 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8659
2025-08-09 00:24:58 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.8735
2025-08-09 00:25:05 - hyperparameter_tuning - INFO - Trial 14: 发现更好的得分 0.8753
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8753
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 168, 'depth': 2, 'learning_rate': 0.09386781465004258, 'l2_leaf_reg': 8.105293596875182, 'bagging_temperature': 0.9891767393669744}
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.8753
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 实际执行试验次数: 25/50
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:25:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_002540.html
2025-08-09 00:25:40 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_002540.html
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 84.80 秒
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:25:40 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8594
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8613
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8613
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8613
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8613
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.8643
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 0.5353231845016033, 'clf__solver': 'lbfgs'}
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.8643
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:25:41 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:41 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_002541.html
2025-08-09 00:25:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_002542.html
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.39 秒
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8484
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8629
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8637
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8637
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.4426973206251916, 'clf__kernel': 'linear'}
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.8637
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 00:25:42 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:25:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_002542.html
2025-08-09 00:25:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_002543.html
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.21 秒
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8403
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8575
2025-08-09 00:25:43 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 9}
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.8621
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/50
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:25:44 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_002544.html
2025-08-09 00:25:44 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_002544.html
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.25 秒
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.8452
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:25:44 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:25:58 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.7970
2025-08-09 00:26:10 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.8001
2025-08-09 00:26:22 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.8016
2025-08-09 00:26:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8025
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (100,), 'clf__alpha': 0.009826250257559992}
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.8025
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/50
2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:26:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:26:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_002653.html
2025-08-09 00:26:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:26:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_002653.html
2025-08-09 00:26:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 69.20 秒
