#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeLong检验实现
用于比较两个ROC曲线的AUC是否存在显著差异
"""

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.metrics import roc_auc_score
from itertools import combinations
try:
    from logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

def delong_roc_variance(ground_truth, predictions):
    """
    计算DeLong方法的ROC方差
    
    Args:
        ground_truth: 真实标签
        predictions: 预测概率
        
    Returns:
        float: ROC方差
    """
    # 将输入转换为numpy数组
    ground_truth = np.array(ground_truth)
    predictions = np.array(predictions)
    
    # 分离正负样本
    order = np.argsort(predictions)
    predictions_sorted = predictions[order]
    ground_truth_sorted = ground_truth[order]
    
    # 计算正负样本数量
    n_pos = np.sum(ground_truth_sorted == 1)
    n_neg = np.sum(ground_truth_sorted == 0)
    
    if n_pos == 0 or n_neg == 0:
        return np.nan
    
    # 计算V10和V01
    V10 = np.zeros(n_pos)
    V01 = np.zeros(n_neg)
    
    pos_indices = np.where(ground_truth_sorted == 1)[0]
    neg_indices = np.where(ground_truth_sorted == 0)[0]
    
    for i, pos_idx in enumerate(pos_indices):
        V10[i] = np.sum(predictions_sorted[pos_idx] > predictions_sorted[neg_indices]) + \
                 0.5 * np.sum(predictions_sorted[pos_idx] == predictions_sorted[neg_indices])
    
    for i, neg_idx in enumerate(neg_indices):
        V01[i] = np.sum(predictions_sorted[neg_idx] < predictions_sorted[pos_indices]) + \
                 0.5 * np.sum(predictions_sorted[neg_idx] == predictions_sorted[pos_indices])
    
    V10 = V10 / n_neg
    V01 = V01 / n_pos
    
    # 计算方差
    S10 = np.var(V10, ddof=1) if len(V10) > 1 else 0
    S01 = np.var(V01, ddof=1) if len(V01) > 1 else 0
    
    variance = (S10 / n_pos) + (S01 / n_neg)
    
    return variance

def delong_roc_test(ground_truth, predictions_1, predictions_2):
    """
    执行DeLong检验比较两个ROC曲线
    
    Args:
        ground_truth: 真实标签
        predictions_1: 模型1的预测概率
        predictions_2: 模型2的预测概率
        
    Returns:
        tuple: (z_score, p_value, auc1, auc2)
    """
    try:
        # 计算AUC
        auc1 = roc_auc_score(ground_truth, predictions_1)
        auc2 = roc_auc_score(ground_truth, predictions_2)
        
        # 计算方差
        var1 = delong_roc_variance(ground_truth, predictions_1)
        var2 = delong_roc_variance(ground_truth, predictions_2)
        
        # 计算协方差（简化处理，假设为0）
        covar = 0
        
        # 计算标准误差
        se = np.sqrt(var1 + var2 - 2 * covar)
        
        if se == 0:
            return 0, 1.0, auc1, auc2
        
        # 计算z分数
        z_score = (auc1 - auc2) / se
        
        # 计算p值（双尾检验）
        p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
        
        return z_score, p_value, auc1, auc2
        
    except Exception as e:
        logger.error(f"DeLong检验计算失败: {e}")
        return 0, 1.0, 0, 0

def perform_delong_comparison(model_data, alpha=0.05):
    """
    对多个模型进行两两DeLong检验比较
    
    Args:
        model_data: 字典，键为模型名，值为包含y_true和y_score的字典
        alpha: 显著性水平，默认0.05
        
    Returns:
        dict: 比较结果
    """
    logger.info(f"开始DeLong检验，比较{len(model_data)}个模型")
    
    model_names = list(model_data.keys())
    results = {
        'pairwise_results': [],
        'summary': {
            'total_comparisons': 0,
            'significant_comparisons': 0,
            'alpha': alpha
        }
    }
    
    # 两两比较
    for model1, model2 in combinations(model_names, 2):
        try:
            data1 = model_data[model1]
            data2 = model_data[model2]
            
            # 确保使用相同的测试集
            y_true1 = data1['y_true']
            y_true2 = data2['y_true']
            
            if not np.array_equal(y_true1, y_true2):
                logger.warning(f"模型{model1}和{model2}的测试集不同，跳过比较")
                continue
            
            y_score1 = data1['y_score']
            y_score2 = data2['y_score']
            
            # 执行DeLong检验
            z_score, p_value, auc1, auc2 = delong_roc_test(y_true1, y_score1, y_score2)
            
            is_significant = p_value < alpha
            
            comparison_result = {
                'model1': model1,
                'model2': model2,
                'auc1': auc1,
                'auc2': auc2,
                'z_score': z_score,
                'p_value': p_value,
                'is_significant': is_significant,
                'alpha': alpha
            }
            
            results['pairwise_results'].append(comparison_result)
            results['summary']['total_comparisons'] += 1
            
            if is_significant:
                results['summary']['significant_comparisons'] += 1
            
            logger.info(f"比较{model1} vs {model2}: AUC1={auc1:.4f}, AUC2={auc2:.4f}, p={p_value:.6f}")
            
        except Exception as e:
            logger.error(f"比较{model1}和{model2}时出错: {e}")
    
    logger.info(f"DeLong检验完成，共{results['summary']['total_comparisons']}个比较")
    return results

def save_delong_results(results, output_path):
    """
    保存DeLong检验结果到文件
    
    Args:
        results: DeLong检验结果
        output_path: 输出路径
    """
    try:
        import json
        from pathlib import Path
        
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON格式
        json_path = output_path / 'delong_test_results.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存CSV格式
        if results['pairwise_results']:
            df = pd.DataFrame(results['pairwise_results'])
            csv_path = output_path / 'delong_test_results.csv'
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        logger.info(f"DeLong检验结果已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"保存DeLong检验结果失败: {e}")
