#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI功能实现模块
包含所有GUI界面的核心功能方法
"""

import sys
import os
import threading
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import numpy as np
import matplotlib
matplotlib.use('TkAgg')  # 设置matplotlib后端
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import optuna

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

from data_preprocessing import DataPreprocessor
from model_training import MODEL_TRAINERS
from thread_safe_gui import ThreadSafeGUI, create_progress_updater, safe_matplotlib_backend_switch

# 导入图表优化模块
try:
    from chart_optimization import optimize_charts, create_optimized_figure, optimize_for_gui, setup_smooth_rendering
    CHART_OPTIMIZATION_AVAILABLE = True
    print("✅ 图表优化模块已加载")
except ImportError:
    CHART_OPTIMIZATION_AVAILABLE = False
    print("⚠️ 图表优化模块不可用，将使用默认设置")

def save_figure_with_layers(fig_or_pdf, save_path_or_fig, format_type='pdf', **kwargs):
    """
    保存图表为保留原始图层的PDF格式或其他格式

    Args:
        fig_or_pdf: matplotlib图形对象或PdfPages对象
        save_path_or_fig: 保存路径(字符串)或图形对象
        format_type: 保存格式 ('pdf' 或 'png')
        **kwargs: 额外的savefig参数
    """
    # 保留原始图层的PDF保存参数
    pdf_params = {
        'bbox_inches': 'tight',
        'dpi': 300,
        'facecolor': 'white',
        'edgecolor': 'none',
        'transparent': False,
        'pad_inches': 0.1,
        'format': 'pdf'
    }

    # 合并用户提供的参数
    pdf_params.update(kwargs)

    if format_type == 'pdf':
        if hasattr(fig_or_pdf, 'savefig'):  # PdfPages对象
            fig_or_pdf.savefig(save_path_or_fig, **pdf_params)
        else:  # matplotlib图形对象
            fig_or_pdf.savefig(save_path_or_fig, **pdf_params)
    else:
        # PNG或其他格式
        png_params = {
            'bbox_inches': 'tight',
            'dpi': 300
        }
        png_params.update(kwargs)
        if hasattr(fig_or_pdf, 'savefig'):
            fig_or_pdf.savefig(save_path_or_fig, **png_params)
        else:
            fig_or_pdf.savefig(save_path_or_fig, **png_params)

class GUIFunctions:
    """GUI功能实现类"""
    
    def __init__(self, gui_instance):
        """
        初始化GUI功能
        
        Args:
            gui_instance: GUI主界面实例
        """
        self.gui = gui_instance
        self.current_data = None
        self.current_target_col = None
        self.trained_models = {}
        self.training_thread = None
        self.is_training = False
        
    def load_data(self):
        """加载数据的完整实现"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.gui.current_data_path.set(file_path)
            self.gui.load_data_file()
    
    def start_training(self):
        """开始模型训练"""
        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中，请等待完成")
            return
        
        # 检查数据是否已加载
        if not self.gui.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return
        
        # 检查是否选择了模型
        selected_models = self.gui.get_selected_models()
        if not selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return
        
        # 在新线程中执行训练
        self.training_thread = threading.Thread(target=self._train_models_thread, args=(selected_models,))
        self.training_thread.daemon = True
        self.training_thread.start()
    
    def _train_models_thread(self, selected_models):
        """在后台线程中训练模型"""
        try:
            self.is_training = True

            # 创建线程安全的GUI更新工具
            gui_updater = ThreadSafeGUI(self.gui.root)

            # 线程安全的GUI更新
            gui_updater.safe_update_status(self.gui.status_text, "正在训练模型...")
            gui_updater.safe_log_message(self.gui.log_message, "开始模型训练")

            # 应用严格复现模式设置（与CLI保持一致）
            try:
                repro_config = self.gui.get_reproducibility_config()
                if repro_config['strict_reproducibility']:
                    from config import REPRODUCIBILITY_CONFIG, apply_reproducibility_env, set_global_seed, RANDOM_SEED

                    # 更新全局配置
                    REPRODUCIBILITY_CONFIG['strict'] = True
                    REPRODUCIBILITY_CONFIG['num_threads'] = repro_config['num_threads']
                    REPRODUCIBILITY_CONFIG['enforce_cpu'] = repro_config['enforce_cpu']

                    # 应用环境设置
                    apply_reproducibility_env(strict=True)

                    # 重新设置全局随机种子
                    set_global_seed(RANDOM_SEED)

                    self.gui.log_message(f"已启用严格复现模式 (线程数: {repro_config['num_threads']}, 强制CPU: {repro_config['enforce_cpu']})")
                else:
                    self.gui.log_message("使用标准训练模式")
            except Exception as e:
                self.gui.log_message(f"应用严格复现模式设置失败: {e}")

            # 自动创建训练会话（如果没有活动会话）
            try:
                from session_utils import get_active_session_id, create_new_session
                from pathlib import Path
                from datetime import datetime

                current_session_id = get_active_session_id()
                if not current_session_id:
                    # 基于数据文件创建会话
                    data_path = self.gui.current_data_path.get()
                    data_name = Path(data_path).stem if data_path else "unknown"
                    session_name = f"训练_{data_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    description = f"自动创建的训练会话，基于数据文件: {data_name}"

                    session_id = create_new_session(session_name, description, auto_activate=True)
                    if session_id:
                        self.gui.log_message(f"自动创建训练会话: {session_name}")
                        # 更新GUI标题
                        self.gui.root.after(0, self.gui.update_session_status)
                    else:
                        self.gui.log_message("警告: 创建训练会话失败，将使用传统保存方式")
            except Exception as e:
                self.gui.log_message(f"警告: 会话管理功能不可用: {e}")

            # 准备数据
            data_path = self.gui.current_data_path.get()
            test_size = self.gui.test_size_var.get()
            random_seed = self.gui.random_seed_var.get()
            scaling_method = self.gui.scaling_var.get()
            
            # 创建数据预处理器
            scaling_method_param = scaling_method if scaling_method != "none" else "standard"
            preprocessor = DataPreprocessor(
                test_size=test_size,
                random_state=random_seed,
                scaling_method=scaling_method_param
            )
            
            # 加载和预处理数据
            X_train, X_test, y_train, y_test = preprocessor.load_and_preprocess(data_path)
            
            # 训练选中的模型
            total_models = len(selected_models)
            for i, model_name in enumerate(selected_models):
                if not self.is_training:  # 检查是否被停止
                    break

                # 线程安全的GUI更新
                gui_updater.safe_log_message(self.gui.log_message, f"正在训练 {model_name}...")
                progress = (i / total_models) * 100
                gui_updater.safe_update_progress(self.gui.training_progress, progress)

                try:
                    # 获取训练器并训练模型
                    trainer = MODEL_TRAINERS[model_name]
                    model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)

                    # 保存训练好的模型（包含训练数据以支持学习曲线）
                    self.trained_models[model_name] = {
                        'model': model,
                        'trainer': trainer,
                        'X_train': X_train,
                        'y_train': y_train,
                        'X_test': X_test,
                        'y_test': y_test
                    }

                    gui_updater.safe_log_message(self.gui.log_message, f"{model_name} 训练完成")

                except Exception as e:
                    gui_updater.safe_log_message(self.gui.log_message, f"{model_name} 训练失败: {e}")

            # 训练完成
            gui_updater.safe_update_progress(self.gui.training_progress, 100)
            gui_updater.safe_update_status(self.gui.status_text, f"训练完成，共训练 {len(self.trained_models)} 个模型")
            gui_updater.safe_log_message(self.gui.log_message, "所有模型训练完成")

            # 如果训练了多个模型，进行DeLong检验比较
            if len(self.trained_models) > 1:
                gui_updater.safe_log_message(self.gui.log_message, "正在进行DeLong检验比较模型性能...")
                try:
                    self._perform_delong_test()
                    gui_updater.safe_log_message(self.gui.log_message, "DeLong检验完成")
                except Exception as e:
                    gui_updater.safe_log_message(self.gui.log_message, f"DeLong检验失败: {e}")

            # 更新可视化模型选择列表
            gui_updater.safe_call(self._update_viz_model_list)

        except Exception as e:
            gui_updater.safe_log_message(self.gui.log_message, f"训练过程出错: {e}")
            gui_updater.safe_show_message("错误", f"训练失败: {e}", "error")
        finally:
            self.is_training = False
    
    def stop_training(self):
        """停止模型训练"""
        if self.is_training:
            self.is_training = False
            self.gui.log_message("用户停止了训练")
            self.gui.status_text.set("训练已停止")
    
    def _update_viz_model_list(self):
        """更新可视化模型选择列表"""
        model_names = list(self.trained_models.keys())
        self.gui.viz_model_combo['values'] = model_names
        if model_names:
            self.gui.viz_model_var.set(model_names[0])
    
    def single_model_visualization(self):
        """单模型可视化（完善版）"""
        selected_model = self.gui.viz_model_var.get()
        self.gui.log_message(f"开始单模型可视化，选择的模型: {selected_model}")

        if not selected_model:
            messagebox.showwarning("警告", "请选择一个模型")
            self.gui.log_message("警告：未选择模型")
            return

        # 首先尝试从缓存加载模型数据
        model_data = self._load_model_from_cache(selected_model)

        if model_data is None:
            # 如果缓存中没有，再检查trained_models
            if selected_model not in self.trained_models:
                messagebox.showwarning("警告", "请先训练模型或选择有效的模型")
                self.gui.log_message(f"警告：模型 {selected_model} 未找到")
                return
            model_data = self.trained_models[selected_model]

        try:
            # 获取模型数据
            model = model_data['model']
            X_test = model_data.get('X_test')
            y_test = model_data.get('y_test') or model_data.get('y_true')
            X_train = model_data.get('X_train', None)
            y_train = model_data.get('y_train', None)

            # 检查关键数据是否存在
            if model is None:
                messagebox.showwarning("警告", "模型对象不存在")
                return

            if X_test is None or y_test is None:
                messagebox.showwarning("警告", "测试数据不完整，无法进行完整可视化")
                self.gui.log_message(f"模型 {selected_model} 缺少测试数据")
                # 尝试显示基本模型信息
                self._show_basic_model_info(selected_model, model)
                return

            self.gui.log_message(f"模型数据获取成功，测试集大小: {X_test.shape}")

            # 创建可视化
            self._create_model_visualization(selected_model, model, X_test, y_test, X_train, y_train)

        except Exception as e:
            error_msg = f"可视化失败: {e}"
            messagebox.showerror("错误", error_msg)
            self.gui.log_message(error_msg)
            import traceback
            traceback.print_exc()

    def _load_model_from_cache(self, model_name):
        """从缓存加载模型数据"""
        try:
            from config import CACHE_PATH
            from joblib import load

            cache_file = CACHE_PATH / f"{model_name}_results.joblib"
            if cache_file.exists():
                data = load(cache_file)
                self.gui.log_message(f"从缓存加载模型 {model_name}")
                return data
            else:
                self.gui.log_message(f"缓存文件不存在: {cache_file}")
                return None

        except Exception as e:
            self.gui.log_message(f"从缓存加载模型失败: {e}")
            return None

    def _show_basic_model_info(self, model_name, model):
        """显示基本模型信息"""
        try:
            import tkinter as tk
            from tkinter import ttk

            # 创建信息窗口
            info_window = tk.Toplevel(self.gui.root)
            info_window.title(f"模型信息 - {model_name}")
            info_window.geometry("500x400")

            # 创建文本框显示信息
            text_widget = tk.Text(info_window, wrap=tk.WORD)
            scrollbar = tk.Scrollbar(info_window, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            # 收集模型信息
            info_text = f"模型名称: {model_name}\n"
            info_text += "=" * 40 + "\n\n"

            # 模型类型
            info_text += f"模型类型: {type(model).__name__}\n\n"

            # 模型参数
            if hasattr(model, 'get_params'):
                params = model.get_params()
                info_text += "主要参数:\n"
                for key, value in list(params.items())[:10]:  # 显示前10个参数
                    info_text += f"  {key}: {value}\n"
                if len(params) > 10:
                    info_text += f"  ... 还有 {len(params) - 10} 个参数\n"
                info_text += "\n"

            # 特征重要性（如果支持）
            if hasattr(model, 'feature_importances_'):
                info_text += "特征重要性: 支持\n"
                importances = model.feature_importances_
                info_text += f"特征数量: {len(importances)}\n"
                info_text += f"最重要特征值: {max(importances):.4f}\n\n"

            # 其他属性
            if hasattr(model, 'n_features_in_'):
                info_text += f"输入特征数: {model.n_features_in_}\n"

            if hasattr(model, 'classes_'):
                info_text += f"类别数: {len(model.classes_)}\n"
                info_text += f"类别: {list(model.classes_)}\n"

            info_text += "\n注意: 由于缺少测试数据，无法显示性能指标和完整可视化。\n"
            info_text += "建议重新训练模型以获得完整的分析结果。"

            text_widget.insert(tk.END, info_text)
            text_widget.config(state=tk.DISABLED)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            self.gui.log_message(f"显示模型 {model_name} 的基本信息")

        except Exception as e:
            self.gui.log_message(f"显示模型信息失败: {e}")

    def _create_model_visualization(self, model_name, model, X_test, y_test, X_train=None, y_train=None):
        """创建模型可视化图表（完善版）"""
        chart_type = self.gui.chart_type_var.get()
        self.gui.log_message(f"创建图表: {chart_type} for {model_name}")

        try:
            # 保存图表数据以便重新生成
            self.gui.current_chart_data = {
                'model_name': model_name,
                'chart_type': chart_type,
                'model': model,
                'X_test': X_test,
                'y_test': y_test,
                'X_train': X_train,
                'y_train': y_train
            }

            # 初始化图表优化
            if CHART_OPTIMIZATION_AVAILABLE:
                optimize_charts()
                setup_smooth_rendering()
                fig, ax = create_optimized_figure(figsize=(12, 8))
            else:
                fig, ax = plt.subplots(figsize=(12, 8))

            if chart_type == "ROC曲线":
                self.gui.log_message("绘制ROC曲线...")
                self._plot_roc_curve(ax, model, X_test, y_test, model_name)
            elif chart_type == "混淆矩阵":
                self.gui.log_message("绘制混淆矩阵...")
                self._plot_confusion_matrix(ax, model, X_test, y_test, model_name)
            elif chart_type == "特征重要性":
                self.gui.log_message("绘制特征重要性...")
                self._plot_feature_importance(ax, model, model_name, X_test)
            elif chart_type == "学习曲线":
                if X_train is not None and y_train is not None:
                    self.gui.log_message("绘制学习曲线...")
                    self._plot_learning_curve(ax, model, X_train, y_train, model_name)
                else:
                    self.gui.log_message("学习曲线需要训练数据")
                    ax.text(0.5, 0.5, "学习曲线需要训练数据，请重新训练模型",
                           ha='center', va='center', transform=ax.transAxes, fontsize=14)
            elif chart_type == "性能比较":
                self.gui.log_message("绘制性能雷达图...")
                self._plot_performance_metrics(ax, model, X_test, y_test, model_name)
            elif chart_type == "SHAP分析":
                self.gui.log_message("启动SHAP分析窗口...")
                self._launch_shap_viewer(model, X_test, model_name)
                # 在主窗口显示提示信息
                ax.text(0.5, 0.5, 'SHAP分析已在新窗口中打开\n\n支持功能:\n• 摘要图\n• 依赖图 (可翻页)\n• 力图 (可翻页)\n• 决策图\n• 瀑布图 (可翻页)\n\n请查看新打开的SHAP分析窗口',
                       ha='center', va='center', transform=ax.transAxes, fontsize=14,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8))
            else:
                self.gui.log_message(f"未实现的图表类型: {chart_type}")
                ax.text(0.5, 0.5, f"图表类型 '{chart_type}' 暂未实现",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)

            self.gui.log_message("图表绘制完成，开始显示...")

            # 优化图表用于GUI显示
            if CHART_OPTIMIZATION_AVAILABLE:
                fig = optimize_for_gui(fig)

            # 在GUI中显示图表
            self._display_chart_in_gui(fig)

        except Exception as e:
            self.gui.log_message(f"图表创建失败: {e}")
            raise
    
    def _plot_roc_curve(self, ax, model, X_test, y_test, model_name):
        """绘制ROC曲线（改进版）"""
        from sklearn.metrics import roc_curve, auc, classification_report, confusion_matrix
        import numpy as np

        # 获取预测概率
        if hasattr(model, 'predict_proba'):
            y_pred_proba = model.predict_proba(X_test)[:, 1]
        else:
            y_pred_proba = model.decision_function(X_test)
            # 将decision_function的输出转换为概率
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
            y_pred_proba = scaler.fit_transform(y_pred_proba.reshape(-1, 1)).flatten()

        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)
        roc_auc = auc(fpr, tpr)

        # 检查数据质量
        class_distribution = np.bincount(y_test)
        is_balanced = min(class_distribution) / max(class_distribution) > 0.3

        # 选择颜色（根据AUC质量）
        if roc_auc >= 0.8:
            color = 'green'
        elif roc_auc >= 0.7:
            color = 'orange'
        else:
            color = 'red'

        # 绘制ROC曲线
        ax.plot(fpr, tpr, color=color, lw=2,
               label=f'{model_name} (AUC = {roc_auc:.3f})')
        ax.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--', alpha=0.5, label='Random')

        # 添加最优工作点
        optimal_idx = np.argmax(tpr - fpr)
        optimal_threshold = thresholds[optimal_idx]
        ax.plot(fpr[optimal_idx], tpr[optimal_idx], 'ro', markersize=8,
               label=f'Optimal (t={optimal_threshold:.3f})')

        # 设置图形属性
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('False Positive Rate (1 - Specificity)')
        ax.set_ylabel('True Positive Rate (Sensitivity)')

        # 根据数据质量调整标题
        balance_info = "Balanced" if is_balanced else "Imbalanced"
        ax.set_title(f'ROC Curve - {model_name}\n({balance_info} Data, n={len(y_test)})')

        ax.legend(loc="lower right", fontsize=9)
        ax.grid(True, alpha=0.3)

        # 添加性能提示
        if roc_auc < 0.6:
            ax.text(0.6, 0.2, 'Poor Performance\nConsider:\n• Feature engineering\n• Different algorithm\n• Data balancing',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                   fontsize=8, verticalalignment='top')
        elif not is_balanced and roc_auc < 0.8:
            ax.text(0.6, 0.2, 'Imbalanced Data\nConsider:\n• Class weighting\n• Resampling\n• Different metrics',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
                   fontsize=8, verticalalignment='top')
    
    def _plot_confusion_matrix(self, ax, model, X_test, y_test, model_name):
        """Plot Confusion Matrix"""
        from sklearn.metrics import confusion_matrix
        import seaborn as sns

        y_pred = model.predict(X_test)
        cm = confusion_matrix(y_test, y_pred)

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                   xticklabels=['Negative', 'Positive'],
                   yticklabels=['Negative', 'Positive'])
        ax.set_title(f'Confusion Matrix - {model_name}')
        ax.set_xlabel('Predicted Label')
        ax.set_ylabel('True Label')
    
    def _plot_feature_importance(self, ax, model, model_name, X_test):
        """绘制特征重要性（改进版）"""
        try:
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_

                # 获取特征名称
                if hasattr(X_test, 'columns'):
                    feature_names = X_test.columns.tolist()
                else:
                    feature_names = [f'Feature_{i}' for i in range(len(importances))]

                # 选择前15个重要特征
                indices = np.argsort(importances)[::-1][:15]
                selected_importances = importances[indices]
                selected_features = [feature_names[i] for i in indices]

                # 创建水平条形图
                import matplotlib.cm as cm
                blues_cmap = cm.get_cmap('Blues')
                colors = blues_cmap(np.linspace(0.4, 0.9, len(selected_importances)))
                bars = ax.barh(range(len(selected_importances)), selected_importances, color=colors)

                # Set labels and title
                ax.set_yticks(range(len(selected_importances)))
                ax.set_yticklabels(selected_features)
                ax.set_xlabel('Importance Score')
                ax.set_title(f'Feature Importance Analysis - {model_name}', fontsize=14, fontweight='bold')

                # Add value labels
                for i, (bar, importance) in enumerate(zip(bars, selected_importances)):
                    ax.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
                           f'{importance:.3f}', ha='left', va='center', fontsize=9)

                # Beautify chart
                ax.grid(True, alpha=0.3, axis='x')
                ax.set_axisbelow(True)
                ax.invert_yaxis()  # Most important features at top

            elif hasattr(model, 'coef_'):
                # 对于线性模型，使用系数的绝对值
                coef = np.abs(model.coef_[0] if model.coef_.ndim > 1 else model.coef_)

                # 获取特征名称
                if hasattr(X_test, 'columns'):
                    feature_names = X_test.columns.tolist()
                else:
                    feature_names = [f'Feature_{i}' for i in range(len(coef))]

                # 选择前15个重要特征
                indices = np.argsort(coef)[::-1][:15]
                selected_coef = coef[indices]
                selected_features = [feature_names[i] for i in indices]

                # 创建水平条形图
                import matplotlib.cm as cm
                reds_cmap = cm.get_cmap('Reds')
                colors = reds_cmap(np.linspace(0.4, 0.9, len(selected_coef)))
                bars = ax.barh(range(len(selected_coef)), selected_coef, color=colors)

                # Set labels and title
                ax.set_yticks(range(len(selected_coef)))
                ax.set_yticklabels(selected_features)
                ax.set_xlabel('Coefficient Absolute Value')
                ax.set_title(f'Feature Coefficient Importance - {model_name}', fontsize=14, fontweight='bold')

                # Add value labels
                for i, (bar, coef_val) in enumerate(zip(bars, selected_coef)):
                    ax.text(bar.get_width() + max(selected_coef) * 0.01,
                           bar.get_y() + bar.get_height()/2,
                           f'{coef_val:.3f}', ha='left', va='center', fontsize=9)

                # Beautify chart
                ax.grid(True, alpha=0.3, axis='x')
                ax.set_axisbelow(True)
                ax.invert_yaxis()

            else:
                ax.text(0.5, 0.5, f'{model_name} does not support feature importance analysis\nSupported model types:\n• Tree models (Random Forest, XGBoost, etc.)\n• Linear models (Logistic Regression, etc.)',
                       ha='center', va='center', transform=ax.transAxes, fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

        except Exception as e:
            ax.text(0.5, 0.5, f'Feature importance analysis failed:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def _plot_learning_curve(self, ax, model, X_train, y_train, model_name):
        """绘制学习曲线"""
        try:
            from sklearn.model_selection import learning_curve

            # 计算学习曲线
            learning_curve_result = learning_curve(
                model, X_train, y_train, cv=5, n_jobs=-1,
                train_sizes=np.linspace(0.1, 1.0, 10),
                scoring='roc_auc', random_state=42
            )
            train_sizes, train_scores, val_scores = learning_curve_result[:3]

            # 计算均值和标准差
            train_mean = np.mean(train_scores, axis=1)
            train_std = np.std(train_scores, axis=1)
            val_mean = np.mean(val_scores, axis=1)
            val_std = np.std(val_scores, axis=1)

            # Plot learning curves
            ax.plot(train_sizes, train_mean, 'o-', color='blue', label='Training Set')
            ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.2, color='blue')

            ax.plot(train_sizes, val_mean, 'o-', color='red', label='Validation Set')
            ax.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, alpha=0.2, color='red')

            # Set labels and title
            ax.set_xlabel('Training Sample Size')
            ax.set_ylabel('AUC Score')
            ax.set_title(f'Learning Curve - {model_name}', fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Add performance analysis text
            final_train_score = train_mean[-1]
            final_val_score = val_mean[-1]
            gap = final_train_score - final_val_score

            if gap > 0.1:
                analysis = "Model may be overfitting\nSuggestion: Reduce model complexity"
                color = "orange"
            elif final_val_score < 0.7:
                analysis = "Low model performance\nSuggestion: Add features or data"
                color = "red"
            else:
                analysis = "Good model performance"
                color = "green"

            ax.text(0.02, 0.98, f'Training AUC: {final_train_score:.3f}\nValidation AUC: {final_val_score:.3f}\nGap: {gap:.3f}\n\n{analysis}',
                   transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.3),
                   fontsize=10)

        except Exception as e:
            ax.text(0.5, 0.5, f'Learning curve generation failed:\n{str(e)}\n\nPossible reasons:\n• Model does not support incremental training\n• Insufficient data\n• Cross-validation failed',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def _plot_performance_metrics(self, ax, model, X_test, y_test, model_name):
        """绘制性能指标雷达图"""
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

            # 计算预测结果
            y_pred = model.predict(X_test)

            # 计算各项指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            # 计算AUC
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_test, y_pred_proba)
            else:
                auc = 0.5  # 默认值

            # 计算特异性
            from sklearn.metrics import confusion_matrix
            tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0

            # Prepare radar chart data
            metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC', 'Specificity']
            values = [accuracy, precision, recall, f1, auc, specificity]

            # Create radar chart
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            values += values[:1]  # Close the shape
            angles += angles[:1]

            ax.clear()
            ax = plt.subplot(111, projection='polar')
            ax.plot(angles, values, 'o-', linewidth=2, color='blue', alpha=0.7)
            ax.fill(angles, values, alpha=0.25, color='blue')

            # Set labels
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metrics)
            ax.set_ylim(0, 1)
            ax.set_title(f'Performance Metrics Radar Chart - {model_name}', fontsize=14, fontweight='bold', pad=20)

            # Add grid
            ax.grid(True)

            # Add value labels
            for angle, value, metric in zip(angles[:-1], values[:-1], metrics):
                ax.text(angle, value + 0.05, f'{value:.3f}',
                       horizontalalignment='center', fontsize=9, fontweight='bold')

        except Exception as e:
            ax.text(0.5, 0.5, f'Performance metrics chart generation failed:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def _launch_shap_viewer(self, model, X_test, model_name):
        """启动SHAP分析查看器"""
        try:
            from gui_shap_viewer import SHAPViewer

            # 创建SHAP查看器实例
            shap_viewer = SHAPViewer(self.gui)

            # 创建SHAP分析窗口
            shap_viewer.create_shap_analysis_window(model_name, model, X_test)

            self.gui.log_message(f"SHAP分析窗口已启动 - {model_name}")

        except ImportError as e:
            self.gui.log_message(f"无法导入SHAP查看器: {e}")
            messagebox.showerror("错误", f"无法启动SHAP分析窗口:\n{e}")
        except Exception as e:
            self.gui.log_message(f"启动SHAP查看器失败: {e}")
            messagebox.showerror("错误", f"启动SHAP分析窗口失败:\n{e}")

    def _plot_shap_analysis(self, ax, model, X_test, model_name):
        """Plot SHAP Analysis (following command-line implementation)"""
        try:
            import shap

            # Follow exact command-line logic - use full X_test dataset
            try:
                explainer = shap.TreeExplainer(model)
                shap_values = explainer.shap_values(X_test)
                explainer_type = "TreeExplainer"
            except Exception as e:
                self.gui.log_message(f"TreeExplainer failed: {e}, falling back to KernelExplainer")
                try:
                    explainer = shap.KernelExplainer(model.predict_proba, X_test)
                    shap_values = explainer.shap_values(X_test)
                    explainer_type = "KernelExplainer"
                except Exception as e2:
                    self.gui.log_message(f"SHAP analysis failed: {e2}")
                    raise Exception(f"SHAP analysis failed: {e2}")

            # Ensure we only use positive class (class 1) SHAP values for summary plot
            # Exact logic from command-line version
            shap_values_for_summary = None
            if isinstance(shap_values, list) and len(shap_values) > 1:
                shap_values_for_summary = shap_values[1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values_for_summary = shap_values[:, :, 1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 2:
                shap_values_for_summary = shap_values  # Already in correct format

            if shap_values_for_summary is None:
                self.gui.log_message(f"Cannot extract SHAP values for summary plot for model '{model_name}'. Skipping SHAP analysis.")
                raise Exception(f"Cannot extract SHAP values for summary plot from model '{model_name}'")

            # Clear the axis and create SHAP summary plot exactly like command-line version
            ax.clear()

            # Create SHAP summary plot exactly like command-line version
            import matplotlib.pyplot as plt
            plt.sca(ax)  # Set current axes

            # Use exact same parameters as command-line version
            shap.summary_plot(
                shap_values_for_summary,
                X_test,
                feature_names=X_test.columns.tolist() if hasattr(X_test, 'columns') else None,
                show=False,
                max_display=15
            )

            # Set title exactly like command-line version
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=14, pad=20)
            plt.tight_layout()

            self.gui.log_message(f"SHAP summary plot completed for {model_name} using {explainer_type}")

        except ImportError:
            ax.text(0.5, 0.5, 'SHAP library not installed\nPlease install: pip install shap',
                   ha='center', va='center', transform=ax.transAxes, fontsize=14,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8))
        except Exception as e:
            ax.text(0.5, 0.5, f'SHAP analysis failed:\n{str(e)}\n\nPossible reasons:\n• Model not supported by SHAP\n• Data format incompatible\n• Insufficient memory\n\nSuggestions:\n• Use supported model types\n• Reduce data dimensions\n• Check data format',
                   ha='center', va='center', transform=ax.transAxes, fontsize=11,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def create_command_line_shap_plots(self, model, X_test, model_name, output_dir):
        """
        Create SHAP plots with mixed approach:
        - Keep current method: summary, decision, waterfall plots
        - Use original method: dependence, feature_importance, force plots
        """
        try:
            import shap
            from pathlib import Path
            import matplotlib.pyplot as plt

            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

            # Follow exact command-line logic from plot_single_model.py
            try:
                explainer = shap.TreeExplainer(model)
                shap_values = explainer.shap_values(X_test)
            except Exception as e:
                self.gui.log_message(f"TreeExplainer failed: {e}, falling back to KernelExplainer")
                try:
                    explainer = shap.KernelExplainer(model.predict_proba, X_test)
                    shap_values = explainer.shap_values(X_test)
                except Exception as e2:
                    self.gui.log_message(f"SHAP analysis failed: {e2}")
                    return False

            # Ensure we only use positive class (class 1) SHAP values for summary plot
            shap_values_for_summary = None
            if isinstance(shap_values, list) and len(shap_values) > 1:
                shap_values_for_summary = shap_values[1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values_for_summary = shap_values[:, :, 1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 2:
                shap_values_for_summary = shap_values  # Already in correct format

            if shap_values_for_summary is None:
                self.gui.log_message(f"Cannot extract SHAP values for summary plot for model '{model_name}'. Skipping SHAP analysis.")
                return False

            # === KEEP CURRENT METHOD ===
            # 1. SHAP Summary Plot (current command-line style)
            fig, ax = plt.subplots(figsize=(10, 6))
            shap.summary_plot(
                shap_values_for_summary,
                X_test,
                feature_names=X_test.columns.tolist() if hasattr(X_test, 'columns') else None,
                show=False,
                max_display=15
            )
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=14, pad=20)
            plt.tight_layout()
            summary_path = output_dir / f'{model_name}_shap_summary.png'
            plt.savefig(summary_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.gui.log_message(f"SHAP summary plot saved: {summary_path}")

            # 2. Decision Plot (current command-line style)
            fig, ax = plt.subplots(figsize=(10, 6))

            # Handle X_test format for decision plot
            if isinstance(X_test, np.ndarray):
                import pandas as pd
                feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
                X_test_df = pd.DataFrame(X_test, columns=feature_names)
                X_test_subset = X_test_df.iloc[:50]
            else:
                X_test_subset = X_test.iloc[:50]

            if isinstance(shap_values, list):
                expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                shap_val = shap_values[1][:50]
                shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                shap_val = shap_values[:, :, 1][:50]
                shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
            else:
                shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)
            plt.title('Decision Plot (Top 50 Samples)')
            plt.tight_layout()
            decision_path = output_dir / f"{model_name}_decision_plot.png"
            plt.savefig(decision_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.gui.log_message(f"Decision plot saved: {decision_path}")

            # 3. Waterfall Plots (current command-line style)
            for idx in [0, 5, 10]:
                fig, ax = plt.subplots(figsize=(10, 6))

                # Handle X_test data format
                if isinstance(X_test, np.ndarray):
                    sample_data = X_test[idx]
                    feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
                else:
                    sample_data = X_test.iloc[idx].values
                    feature_names = list(X_test.columns)

                if isinstance(shap_values, list):
                    expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                    shap_val = shap_values[1][idx]
                    exp = shap.Explanation(values=shap_val,
                                           base_values=expected_val,
                                           data=sample_data,
                                           feature_names=feature_names)
                elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                    expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                    shap_val = shap_values[idx, :, 1]
                    exp = shap.Explanation(values=shap_val,
                                           base_values=expected_val,
                                           data=sample_data,
                                           feature_names=feature_names)
                else:
                    exp = shap.Explanation(values=shap_values[idx],
                                           base_values=explainer.expected_value,
                                           data=sample_data,
                                           feature_names=feature_names)
                shap.plots.waterfall(exp, max_display=15, show=False)
                plt.title(f'Waterfall Plot (Sample {idx})')
                plt.tight_layout()
                waterfall_path = output_dir / f"{model_name}_waterfall_{idx}.png"
                plt.savefig(waterfall_path, dpi=300, bbox_inches='tight')
                plt.close()
                self.gui.log_message(f"Waterfall plot saved: {waterfall_path}")

            # === USE ORIGINAL METHOD ===
            # 4. Feature Importance (original enhanced_shap_visualization style)
            self._create_original_feature_importance(shap_values, X_test, model_name, output_dir)

            # 5. Dependence Plots (original enhanced_shap_visualization style)
            self._create_original_dependence_plots(shap_values, X_test, model_name, output_dir)

            # 6. Force Plots (original enhanced_shap_visualization style)
            self._create_original_force_plots(shap_values, X_test, model_name, output_dir)

            return True

        except Exception as e:
            self.gui.log_message(f"Command-line SHAP plots creation failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_original_feature_importance(self, shap_values, X_test, model_name, output_dir):
        """Create feature importance plot using original enhanced_shap_visualization style"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd

            # Handle SHAP values format
            if hasattr(shap_values, 'values'):
                importance = np.abs(shap_values.values).mean(0)
            else:
                importance = np.abs(shap_values).mean(0)

            # Handle multi-dimensional case
            if len(importance.shape) > 1:
                importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

            # Create DataFrame and sort
            feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{i}' for i in range(X_test.shape[1])]
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': importance
            }).sort_values('importance', ascending=True)

            # Take top 12 important features
            top_features = importance_df.tail(12)

            # Create figure
            fig, ax = plt.subplots(figsize=(10, 8))

            # Create color gradient
            import matplotlib.cm as cm
            blues_cmap = cm.get_cmap('Blues')
            colors = blues_cmap(np.linspace(0.4, 0.9, len(top_features)))

            # Draw horizontal bar chart
            bars = ax.barh(range(len(top_features)), top_features['importance'],
                          color=colors, alpha=0.8, height=0.7)

            # Ensure y-axis shows feature names
            ax.set_yticks(range(len(top_features)))
            ax.set_yticklabels(top_features['feature'], fontsize=11)

            # Add value labels
            for i, (bar, value) in enumerate(zip(bars, top_features['importance'])):
                ax.text(value + max(top_features['importance']) * 0.01, i,
                       f'{value:.3f}', va='center', ha='left', fontsize=9, fontweight='bold')

            # Set labels and title
            ax.set_xlabel('Mean |SHAP Value|', fontsize=12, fontweight='bold')
            ax.set_title(f'{model_name} - SHAP Feature Importance', fontsize=16, fontweight='bold', pad=20)

            # Add grid lines
            ax.grid(True, alpha=0.3, axis='x', linestyle='-', linewidth=0.5)
            ax.set_axisbelow(True)

            # Set background color
            ax.set_facecolor('#FAFAFA')

            # Remove top and right borders
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_color('#CCCCCC')
            ax.spines['bottom'].set_color('#CCCCCC')

            # Set x-axis range, leave space to display values
            ax.set_xlim(0, max(top_features['importance']) * 1.15)

            plt.tight_layout()

            importance_path = output_dir / f'{model_name}_feature_importance.png'
            plt.savefig(importance_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            self.gui.log_message(f"Original feature importance plot saved: {importance_path}")
            return str(importance_path)

        except Exception as e:
            self.gui.log_message(f"Failed to create original feature importance plot: {e}")
            return None

    def _create_original_dependence_plots(self, shap_values, X_test, model_name, output_dir):
        """Create dependence plots using original enhanced_shap_visualization style"""
        try:
            import matplotlib.pyplot as plt

            dependence_plots = []

            # Calculate feature importance
            if hasattr(shap_values, 'values'):
                importance = np.abs(shap_values.values).mean(0)
            else:
                importance = np.abs(shap_values).mean(0)

            # Handle multi-dimensional case
            if len(importance.shape) > 1:
                importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

            # Get top 5 features
            top_features_idx = np.argsort(importance)[::-1][:5]

            for feature_idx in top_features_idx:
                try:
                    plt.figure(figsize=(10, 6))

                    feature_name = X_test.columns[feature_idx] if hasattr(X_test, 'columns') else f'feature_{feature_idx}'
                    feature_values = X_test.iloc[:, feature_idx].values if hasattr(X_test, 'iloc') else X_test[:, feature_idx]

                    if hasattr(shap_values, 'values'):
                        shap_vals = shap_values.values[:, feature_idx]
                    else:
                        shap_vals = shap_values[:, feature_idx]

                    # Handle multi-class case
                    if len(shap_vals.shape) > 1:
                        shap_vals = shap_vals[:, 1] if shap_vals.shape[1] > 1 else shap_vals[:, 0]

                    # Create scatter plot
                    plt.scatter(feature_values, shap_vals, alpha=0.6, s=20)
                    plt.xlabel(f'{feature_name} (Feature Value)')
                    plt.ylabel(f'SHAP Value for {feature_name}')
                    plt.title(f'{model_name} - Dependence Plot: {feature_name}')
                    plt.grid(True, alpha=0.3)

                    # Add trend line
                    try:
                        z = np.polyfit(feature_values, shap_vals, 1)
                        p = np.poly1d(z)
                        plt.plot(feature_values, p(feature_values), "r--", alpha=0.8, linewidth=2)
                    except:
                        pass

                    plt.tight_layout()

                    dependence_path = output_dir / f'{model_name}_dependence_{feature_name}.png'
                    plt.savefig(dependence_path, dpi=300, bbox_inches='tight', facecolor='white')
                    plt.close()

                    dependence_plots.append(str(dependence_path))
                    self.gui.log_message(f"Original dependence plot saved: {dependence_path}")

                except Exception as e:
                    self.gui.log_message(f"Failed to create dependence plot for {feature_name}: {e}")
                    continue

            return dependence_plots

        except Exception as e:
            self.gui.log_message(f"Failed to create original dependence plots: {e}")
            return []

    def _create_original_force_plots(self, shap_values, X_test, model_name, output_dir):
        """Create force plots using original enhanced_shap_visualization style"""
        try:
            import matplotlib.pyplot as plt

            force_plots = []

            # Create force plots for first 3 samples
            for i in range(min(3, len(X_test))):
                try:
                    # 使用更合适的尺寸比例
                    fig, ax = plt.subplots(figsize=(10, 6))
                    fig.subplots_adjust(left=0.25, right=0.95, top=0.9, bottom=0.15)  # 调整边距

                    # Create force plot
                    if hasattr(shap_values, 'values'):
                        values = shap_values.values[i]
                        base_value = shap_values.base_values[i] if hasattr(shap_values, 'base_values') else 0
                    else:
                        values = shap_values[i]
                        base_value = 0

                    # Handle multi-class case
                    if len(values.shape) > 1:
                        values = values[:, 1] if values.shape[1] > 1 else values[:, 0]
                        if hasattr(base_value, '__len__') and hasattr(base_value, '__getitem__'):
                            try:
                                if isinstance(base_value, (list, tuple, np.ndarray)) and len(base_value) > 1:
                                    base_value = base_value[1]
                                elif isinstance(base_value, (list, tuple, np.ndarray)) and len(base_value) == 1:
                                    base_value = base_value[0]
                            except (IndexError, TypeError):
                                pass

                    # Manual force plot effect
                    feature_values = X_test.iloc[i].values if hasattr(X_test, 'iloc') else X_test[i]
                    feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{j}' for j in range(X_test.shape[1])]

                    # Sort features by importance
                    importance_order = np.argsort(np.abs(values))[::-1][:10]  # Top 10 important features

                    # Create bar chart showing SHAP values
                    colors = ['red' if v > 0 else 'blue' for v in values[importance_order]]
                    ax.barh(range(len(importance_order)), values[importance_order], color=colors, alpha=0.7)

                    # Add feature names and values
                    labels = [f"{feature_names[idx]} = {feature_values[idx]:.3f}" for idx in importance_order]
                    ax.set_yticks(range(len(importance_order)))
                    ax.set_yticklabels(labels)
                    ax.set_xlabel('SHAP Value')
                    ax.set_title(f'{model_name} - Force Plot (Sample {i+1})', fontsize=12)
                    ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
                    ax.grid(True, alpha=0.3)

                    # 重新设计x轴范围计算，确保合理的显示比例
                    selected_values = values[importance_order]
                    if len(selected_values) > 0:
                        # 计算SHAP值的绝对值最大值
                        max_abs_shap = np.max(np.abs(selected_values))

                        if max_abs_shap > 0:
                            # 设置对称的x轴范围，确保有足够的显示空间
                            margin = max_abs_shap * 0.3  # 30%的边距
                            ax.set_xlim(-max_abs_shap - margin, max_abs_shap + margin)
                        else:
                            # 如果所有值都为0，设置默认范围
                            ax.set_xlim(-0.1, 0.1)

                    # 设置合适的y轴范围，避免图表被拉伸
                    ax.set_ylim(-0.5, len(importance_order) - 0.5)

                    # 调整图表的纵横比，使其更适合显示
                    ax.set_aspect('auto')

                    plt.tight_layout()

                    force_path = output_dir / f'{model_name}_force_plot_sample_{i+1}.png'
                    plt.savefig(force_path, dpi=300, bbox_inches='tight', facecolor='white')
                    plt.close(fig)

                    force_plots.append(str(force_path))
                    self.gui.log_message(f"Original force plot saved: {force_path}")

                except Exception as e:
                    self.gui.log_message(f"Failed to create force plot for sample {i+1}: {e}")
                    continue

            return force_plots

        except Exception as e:
            self.gui.log_message(f"Failed to create original force plots: {e}")
            return []

    def _display_chart_in_gui(self, fig):
        """在GUI中显示图表（增强版，支持滑动条和缩放）"""
        try:
            # 清除之前的图表
            if hasattr(self.gui, 'chart_canvas') and self.gui.chart_canvas:
                self.gui.chart_canvas.get_tk_widget().destroy()
                self.gui.chart_canvas = None

            # 清除滚动条
            if hasattr(self.gui, 'chart_scrollbars'):
                for scrollbar in self.gui.chart_scrollbars:
                    scrollbar.destroy()
                self.gui.chart_scrollbars = []

            # 获取可视化选项卡
            viz_tab_index = 2  # 结果可视化选项卡的索引
            if len(self.gui.notebook.tabs()) > viz_tab_index:
                chart_frame = self.gui.notebook.nametowidget(self.gui.notebook.tabs()[viz_tab_index])
            else:
                self.gui.log_message(f"无法找到可视化选项卡，总选项卡数: {len(self.gui.notebook.tabs())}")
                return

            # 找到图表显示框架
            chart_display_frame = None
            for child in chart_frame.winfo_children():
                if isinstance(child, ttk.LabelFrame):
                    label_text = child.cget('text')
                    if '图表显示' in label_text:
                        chart_display_frame = child
                        break

            if chart_display_frame:
                # 查找并保留状态框架
                status_frame = None
                for child in chart_display_frame.winfo_children():
                    if isinstance(child, ttk.Frame):
                        # 检查是否是状态框架（包含状态标签）
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.Label) and hasattr(grandchild, 'cget'):
                                try:
                                    if 'chart_status_var' in str(grandchild.cget('textvariable')):
                                        status_frame = child
                                        break
                                except:
                                    pass
                        if status_frame:
                            break

                # 清除除状态框架外的所有内容
                for child in list(chart_display_frame.winfo_children()):
                    if child != status_frame:
                        child.destroy()

                # 创建带滚动条的图表容器
                self._create_scrollable_chart_container(chart_display_frame, fig)

                self.gui.log_message("图表显示成功（支持滚动和缩放）")
            else:
                self.gui.log_message("无法找到图表显示区域")

        except Exception as e:
            self.gui.log_message(f"图表显示失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_scrollable_chart_container(self, parent_frame, fig):
        """创建支持滚动和缩放的图表容器（增强版）"""
        # 创建主容器框架
        main_container = ttk.Frame(parent_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建画布和滚动条
        canvas_widget = tk.Canvas(main_container, bg='white', highlightthickness=0)

        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(main_container, orient=tk.VERTICAL, command=canvas_widget.yview)
        canvas_widget.configure(yscrollcommand=v_scrollbar.set)

        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(main_container, orient=tk.HORIZONTAL, command=canvas_widget.xview)
        canvas_widget.configure(xscrollcommand=h_scrollbar.set)

        # 布局滚动条和画布
        canvas_widget.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # 配置网格权重
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)

        # 创建图表框架
        chart_frame = ttk.Frame(canvas_widget)

        # 创建matplotlib画布（优化渲染）
        self.gui.chart_canvas = FigureCanvasTkAgg(fig, chart_frame)

        # 优化matplotlib渲染设置
        fig.patch.set_facecolor('white')
        self.gui.chart_canvas.draw()
        chart_widget = self.gui.chart_canvas.get_tk_widget()
        chart_widget.pack(fill=tk.BOTH, expand=True)

        # 添加工具栏（包含缩放功能）
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X, pady=2)

        try:
            from matplotlib.backends._backend_tk import NavigationToolbar2Tk
        except ImportError:
            try:
                from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
            except ImportError:
                NavigationToolbar2Tk = None

        if NavigationToolbar2Tk is not None:
            toolbar = NavigationToolbar2Tk(self.gui.chart_canvas, toolbar_frame)
            toolbar.update()

        # 添加自定义缩放按钮
        zoom_frame = ttk.Frame(toolbar_frame)
        zoom_frame.pack(side=tk.RIGHT, padx=5)

        ttk.Button(zoom_frame, text="放大", command=lambda: self._zoom_chart(1.2)).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="缩小", command=lambda: self._zoom_chart(0.8)).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="重置", command=lambda: self._reset_chart_zoom()).pack(side=tk.LEFT, padx=2)

        # 将图表框架添加到画布
        canvas_window = canvas_widget.create_window(0, 0, anchor="nw", window=chart_frame)

        # 更新滚动区域
        def configure_scroll_region(event=None):
            canvas_widget.configure(scrollregion=canvas_widget.bbox("all"))

        def configure_canvas_size(event=None):
            # 获取画布大小
            canvas_width = canvas_widget.winfo_width()
            canvas_height = canvas_widget.winfo_height()

            # 获取图表框架的实际大小
            chart_frame.update_idletasks()
            frame_width = chart_frame.winfo_reqwidth()
            frame_height = chart_frame.winfo_reqheight()

            # 设置画布窗口大小
            canvas_widget.itemconfig(canvas_window, width=max(canvas_width, frame_width),
                                    height=max(canvas_height, frame_height))

        chart_frame.bind('<Configure>', configure_scroll_region)
        canvas_widget.bind('<Configure>', configure_canvas_size)

        # 增强的鼠标事件处理
        self._setup_enhanced_mouse_events(canvas_widget, chart_widget)

        # 保存滚动条引用
        if not hasattr(self.gui, 'chart_scrollbars'):
            self.gui.chart_scrollbars = []
        self.gui.chart_scrollbars = [v_scrollbar, h_scrollbar]

        # 保存缩放相关属性
        self.gui.chart_zoom_factor = 1.0
        self.gui.chart_original_size = fig.get_size_inches()

        # 保存画布引用用于鼠标事件
        self.gui.chart_canvas_widget = canvas_widget
        self.gui.chart_widget = chart_widget

    def _setup_enhanced_mouse_events(self, canvas_widget, chart_widget):
        """设置增强的鼠标事件处理"""
        # 鼠标位置跟踪
        self.gui.mouse_in_chart = False

        def on_enter(event):
            """鼠标进入图表区域"""
            self.gui.mouse_in_chart = True
            canvas_widget.focus_set()  # 获取焦点以接收键盘事件

        def on_leave(event):
            """鼠标离开图表区域"""
            self.gui.mouse_in_chart = False

        def on_mousewheel(event):
            """增强的鼠标滚轮事件处理"""
            if not self.gui.mouse_in_chart:
                return

            # 检查Ctrl键状态
            ctrl_pressed = event.state & 0x4

            if ctrl_pressed:
                # Ctrl+滚轮：缩放功能
                if event.delta > 0:
                    self._zoom_chart(1.1)  # 放大
                else:
                    self._zoom_chart(0.9)  # 缩小
            else:
                # 普通滚轮：滚动功能
                if event.state & 0x1:  # Shift键按下时水平滚动
                    canvas_widget.xview_scroll(int(-1 * (event.delta / 120)), "units")
                else:  # 垂直滚动
                    canvas_widget.yview_scroll(int(-1 * (event.delta / 120)), "units")

        def on_motion(event):
            """鼠标移动事件（可用于显示坐标等）"""
            pass  # 预留接口

        # 绑定事件到画布和图表组件
        for widget in [canvas_widget, chart_widget]:
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)
            widget.bind("<MouseWheel>", on_mousewheel)
            widget.bind("<Motion>", on_motion)

        # 绑定键盘事件（用于检测Ctrl键状态）
        canvas_widget.bind("<KeyPress>", lambda e: None)
        canvas_widget.bind("<KeyRelease>", lambda e: None)

    def _zoom_chart(self, factor):
        """缩放图表（重新实现版）"""
        if hasattr(self.gui, 'chart_canvas') and self.gui.chart_canvas:
            try:
                # 限制缩放范围
                new_zoom_factor = self.gui.chart_zoom_factor * factor
                if new_zoom_factor < 0.1:  # 最小缩放
                    new_zoom_factor = 0.1
                elif new_zoom_factor > 10.0:  # 最大缩放
                    new_zoom_factor = 10.0

                self.gui.chart_zoom_factor = new_zoom_factor

                # 完全重新创建图表而不是缩放现有图表
                self._recreate_chart_with_zoom()

                # 更新状态显示
                if hasattr(self.gui, 'chart_status_var'):
                    self.gui.chart_status_var.set(f"缩放: {self.gui.chart_zoom_factor:.1f}x")

            except Exception as e:
                self.gui.log_message(f"缩放失败: {e}")
                # 如果缩放失败，尝试重新生成图表
                self._regenerate_current_chart()

    def _recreate_chart_with_zoom(self):
        """完全重新创建图表（解决堆叠问题的最终方案）"""
        try:
            if not hasattr(self.gui, 'current_chart_data'):
                return

            # 获取当前图表数据
            chart_data = self.gui.current_chart_data
            model_name = chart_data.get('model_name')
            chart_type = chart_data.get('chart_type')
            model = chart_data.get('model')
            X_test = chart_data.get('X_test')
            y_test = chart_data.get('y_test')
            X_train = chart_data.get('X_train')
            y_train = chart_data.get('y_train')

            # 计算新的图形尺寸
            original_size = self.gui.chart_original_size
            new_size = (original_size[0] * self.gui.chart_zoom_factor,
                       original_size[1] * self.gui.chart_zoom_factor)

            # 创建全新的图形
            if CHART_OPTIMIZATION_AVAILABLE:
                new_fig, ax = create_optimized_figure(figsize=new_size)
            else:
                new_fig, ax = plt.subplots(figsize=new_size)

            # 根据图表类型重新绘制
            if chart_type == "ROC曲线":
                self._plot_roc_curve(ax, model, X_test, y_test, model_name)
            elif chart_type == "混淆矩阵":
                self._plot_confusion_matrix(ax, model, X_test, y_test, model_name)
            elif chart_type == "特征重要性":
                self._plot_feature_importance(ax, model, model_name, X_test)
            elif chart_type == "学习曲线":
                if X_train is not None and y_train is not None:
                    self._plot_learning_curve(ax, model, X_train, y_train, model_name)
            elif chart_type == "性能比较":
                self._plot_performance_metrics(ax, model, X_test, y_test, model_name)
            elif chart_type == "SHAP分析":
                self._plot_shap_analysis(ax, model, X_test, model_name)

            # 应用优化设置
            if CHART_OPTIMIZATION_AVAILABLE:
                new_fig = optimize_for_gui(new_fig)
            else:
                new_fig.tight_layout()

            # 完全替换旧的图表显示
            self._replace_chart_display(new_fig)

        except Exception as e:
            self.gui.log_message(f"重新创建图表失败: {e}")

    def _replace_chart_display(self, new_fig):
        """完全替换图表显示"""
        try:
            # 销毁旧的画布
            if hasattr(self.gui, 'chart_canvas') and self.gui.chart_canvas:
                old_widget = self.gui.chart_canvas.get_tk_widget()
                old_widget.destroy()

            # 找到图表容器
            chart_container = None
            viz_tab_index = 2  # 结果可视化选项卡的索引
            if len(self.gui.notebook.tabs()) > viz_tab_index:
                chart_frame = self.gui.notebook.nametowidget(self.gui.notebook.tabs()[viz_tab_index])

                # 查找图表显示框架
                for child in chart_frame.winfo_children():
                    if isinstance(child, ttk.LabelFrame):
                        widget_text = child.cget('text')
                        if '图表显示' in widget_text:
                            # 找到图表容器
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Frame):
                                    # 检查是否包含图表内容
                                    if len(grandchild.winfo_children()) > 0:
                                        chart_container = grandchild
                                        break
                            break

            if chart_container:
                # 清除容器中的所有内容
                for widget in chart_container.winfo_children():
                    widget.destroy()

                # 重新创建滚动容器
                self._create_scrollable_chart_container(chart_container, new_fig)

                self.gui.log_message("图表显示已完全更新")
            else:
                self.gui.log_message("未找到图表容器")

        except Exception as e:
            self.gui.log_message(f"替换图表显示失败: {e}")

    def _recreate_chart_content(self, fig):
        """重新创建图表内容"""
        try:
            # 保存当前图表的数据和设置
            if hasattr(self.gui, 'current_chart_data'):
                chart_data = self.gui.current_chart_data
                model_name = chart_data.get('model_name')
                chart_type = chart_data.get('chart_type')
                model = chart_data.get('model')
                X_test = chart_data.get('X_test')
                y_test = chart_data.get('y_test')
                X_train = chart_data.get('X_train')
                y_train = chart_data.get('y_train')

                # 创建新的子图
                ax = fig.add_subplot(111)

                # 根据图表类型重新绘制
                if chart_type == "ROC曲线":
                    self._plot_roc_curve(ax, model, X_test, y_test, model_name)
                elif chart_type == "混淆矩阵":
                    self._plot_confusion_matrix(ax, model, X_test, y_test, model_name)
                elif chart_type == "特征重要性":
                    self._plot_feature_importance(ax, model, model_name, X_test)
                elif chart_type == "学习曲线":
                    if X_train is not None and y_train is not None:
                        self._plot_learning_curve(ax, model, X_train, y_train, model_name)
                elif chart_type == "性能比较":
                    self._plot_performance_metrics(ax, model, X_test, y_test, model_name)
                elif chart_type == "SHAP分析":
                    self._plot_shap_analysis(ax, model, X_test, model_name)

                # 应用优化设置
                if CHART_OPTIMIZATION_AVAILABLE:
                    fig = optimize_for_gui(fig)
                else:
                    fig.tight_layout()

        except Exception as e:
            self.gui.log_message(f"重新创建图表内容失败: {e}")

    def _regenerate_current_chart(self):
        """重新生成当前图表"""
        try:
            if hasattr(self.gui, 'current_chart_data'):
                chart_data = self.gui.current_chart_data
                model_name = chart_data.get('model_name')
                model = chart_data.get('model')
                X_test = chart_data.get('X_test')
                y_test = chart_data.get('y_test')
                X_train = chart_data.get('X_train')
                y_train = chart_data.get('y_train')

                # 重新创建图表
                self._create_model_visualization(model_name, model, X_test, y_test, X_train, y_train)
        except Exception as e:
            self.gui.log_message(f"重新生成图表失败: {e}")

    def _update_scroll_region(self):
        """更新滚动区域"""
        try:
            if hasattr(self.gui, 'chart_canvas_widget'):
                self.gui.chart_canvas_widget.configure(scrollregion=self.gui.chart_canvas_widget.bbox("all"))
        except Exception as e:
            pass  # 忽略更新错误

    def _reset_chart_zoom(self):
        """重置图表缩放（重新实现版）"""
        if hasattr(self.gui, 'chart_canvas') and self.gui.chart_canvas:
            try:
                # 重置缩放因子
                self.gui.chart_zoom_factor = 1.0

                # 完全重新创建图表
                self._recreate_chart_with_zoom()

                # 更新状态显示
                if hasattr(self.gui, 'chart_status_var'):
                    self.gui.chart_status_var.set("缩放已重置")

                self.gui.log_message("图表缩放已重置")

            except Exception as e:
                self.gui.log_message(f"重置缩放失败: {e}")
                # 如果重置失败，尝试重新生成图表
                self._regenerate_current_chart()
    
    def model_comparison(self):
        """模型比较可视化"""
        if len(self.trained_models) < 2:
            messagebox.showwarning("警告", "需要至少训练2个模型才能进行比较")
            return
        
        try:
            self._create_comparison_chart()
        except Exception as e:
            messagebox.showerror("错误", f"模型比较失败: {e}")
    
    def _create_comparison_chart(self):
        """创建模型比较图表"""
        from sklearn.metrics import accuracy_score, roc_auc_score
        
        # 收集所有模型的性能指标
        model_names = []
        accuracies = []
        aucs = []
        
        for model_name, model_data in self.trained_models.items():
            model = model_data['model']
            X_test = model_data['X_test']
            y_test = model_data['y_test']
            
            # 计算指标
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc_score = roc_auc_score(y_test, y_pred_proba)
            else:
                auc_score = accuracy  # 如果没有概率预测，使用准确率
            
            model_names.append(model_name)
            accuracies.append(accuracy)
            aucs.append(auc_score)
        
        # 创建比较图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 准确率比较
        bars1 = ax1.bar(model_names, accuracies, color='skyblue')
        ax1.set_title('Model Accuracy Comparison')
        ax1.set_ylabel('Accuracy')
        ax1.set_ylim(0, 1)
        ax1.tick_params(axis='x', rotation=45)
        
        # 在柱状图上显示数值
        for bar, acc in zip(bars1, accuracies):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom')
        
        # AUC比较
        bars2 = ax2.bar(model_names, aucs, color='lightcoral')
        ax2.set_title('Model AUC Comparison')
        ax2.set_ylabel('AUC')
        ax2.set_ylim(0, 1)
        ax2.tick_params(axis='x', rotation=45)
        
        # 在柱状图上显示数值
        for bar, auc in zip(bars2, aucs):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{auc:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 在GUI中显示
        self._display_chart_in_gui(fig)
    
    def refresh_chart(self):
        """刷新当前图表"""
        selected_model = self.gui.viz_model_var.get()
        if selected_model:
            self.gui.log_message(f"刷新图表，模型: {selected_model}")
            self.single_model_visualization()
        else:
            messagebox.showwarning("警告", "请先选择一个模型")
            self.gui.log_message("刷新图表失败：未选择模型")
    
    def save_chart(self):
        """保存当前图表 - 增强版本支持PDF批量保存"""
        if not hasattr(self.gui, 'chart_canvas') or not self.gui.chart_canvas:
            messagebox.showwarning("警告", "没有可保存的图表")
            return

        # 创建保存选项对话框
        save_dialog = tk.Toplevel(self.gui.root)
        save_dialog.title("保存图表")
        save_dialog.geometry("400x300")
        save_dialog.resizable(False, False)
        save_dialog.transient(self.gui.root)
        save_dialog.grab_set()

        # 保存选项
        ttk.Label(save_dialog, text="选择保存方式:", font=("Arial", 12, "bold")).pack(pady=10)

        save_option = tk.StringVar(value="current")

        ttk.Radiobutton(save_dialog, text="仅保存当前图表",
                       variable=save_option, value="current").pack(anchor=tk.W, padx=20, pady=5)
        ttk.Radiobutton(save_dialog, text="保存当前模型所有图表到一个PDF",
                       variable=save_option, value="all_single_pdf").pack(anchor=tk.W, padx=20, pady=5)
        ttk.Radiobutton(save_dialog, text="保存当前模型所有图表到不同PDF",
                       variable=save_option, value="all_separate_pdf").pack(anchor=tk.W, padx=20, pady=5)

        # 格式选择
        ttk.Label(save_dialog, text="文件格式:", font=("Arial", 10)).pack(pady=(20, 5))
        format_var = tk.StringVar(value="png")
        format_frame = ttk.Frame(save_dialog)
        format_frame.pack(pady=5)

        ttk.Radiobutton(format_frame, text="PNG", variable=format_var, value="png").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(format_frame, text="PDF", variable=format_var, value="pdf").pack(side=tk.LEFT, padx=10)

        # 按钮框架
        button_frame = ttk.Frame(save_dialog)
        button_frame.pack(pady=20)

        def execute_save():
            option = save_option.get()
            file_format = format_var.get()

            try:
                if option == "current":
                    self._save_current_chart(file_format)
                elif option == "all_single_pdf":
                    self._save_all_charts_single_pdf()
                elif option == "all_separate_pdf":
                    self._save_all_charts_separate_pdf()
                save_dialog.destroy()
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

        ttk.Button(button_frame, text="保存", command=execute_save).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=save_dialog.destroy).pack(side=tk.LEFT, padx=5)

    def _save_current_chart(self, file_format="png"):
        """保存当前显示的图表"""
        if file_format == "png":
            filetypes = [("PNG files", "*.png"), ("All files", "*.*")]
            defaultextension = ".png"
        else:
            filetypes = [("PDF files", "*.pdf"), ("All files", "*.*")]
            defaultextension = ".pdf"

        file_path = filedialog.asksaveasfilename(
            title="保存当前图表",
            defaultextension=defaultextension,
            filetypes=filetypes
        )

        if file_path:
            if file_format == "pdf":
                # 保留原始图层的PDF保存参数
                self.gui.chart_canvas.figure.savefig(file_path,
                                                    format='pdf',
                                                    dpi=300,
                                                    bbox_inches='tight',
                                                    facecolor='white',
                                                    edgecolor='none',
                                                    transparent=False,
                                                    pad_inches=0.1,
                                                    metadata={'Creator': 'Multi-Model Analysis Tool',
                                                            'Subject': 'Model Visualization Chart',
                                                            'Title': 'Machine Learning Model Chart',
                                                            'Keywords': 'Machine Learning, Visualization, Analysis'})
            else:
                self.gui.chart_canvas.figure.savefig(file_path, dpi=300, bbox_inches='tight')

            format_name = "PDF" if file_format == "pdf" else "PNG"
            messagebox.showinfo("成功", f"{format_name}格式图表已保存到: {file_path}")

    def _save_all_charts_single_pdf(self):
        """保存当前模型的所有图表到一个PDF文件"""
        # 检查是否有当前图表数据
        if not hasattr(self.gui, 'current_chart_data') or not self.gui.current_chart_data:
            messagebox.showwarning("警告", "没有可用的图表数据")
            return

        chart_data = self.gui.current_chart_data
        model_name = chart_data.get('model_name', 'Unknown')
        model = chart_data.get('model')
        X_test = chart_data.get('X_test')
        y_test = chart_data.get('y_test')
        X_train = chart_data.get('X_train')
        y_train = chart_data.get('y_train')

        if model is None or X_test is None or y_test is None:
            messagebox.showwarning("警告", "图表数据不完整，无法生成完整报告")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存模型图表报告",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
            initialfile=f"{model_name}_完整图表报告.pdf"
        )

        if not file_path:
            return

        try:
            self._generate_single_pdf_report(model_name, model, X_test, y_test, X_train, y_train, file_path)
            messagebox.showinfo("成功", f"模型图表报告已保存到: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"生成PDF报告失败: {e}")
            raise

    def _save_all_charts_separate_pdf(self):
        """保存当前模型的所有图表到不同的PDF文件"""
        # 检查是否有当前图表数据
        if not hasattr(self.gui, 'current_chart_data') or not self.gui.current_chart_data:
            messagebox.showwarning("警告", "没有可用的图表数据")
            return

        chart_data = self.gui.current_chart_data
        model_name = chart_data.get('model_name', 'Unknown')
        model = chart_data.get('model')
        X_test = chart_data.get('X_test')
        y_test = chart_data.get('y_test')
        X_train = chart_data.get('X_train')
        y_train = chart_data.get('y_train')

        if model is None or X_test is None or y_test is None:
            messagebox.showwarning("警告", "图表数据不完整，无法生成完整报告")
            return

        # 选择保存目录
        save_dir = filedialog.askdirectory(
            title="选择保存目录",
            initialdir=os.path.expanduser("~")
        )

        if not save_dir:
            return

        try:
            saved_files = self._generate_separate_pdf_reports(model_name, model, X_test, y_test, X_train, y_train, save_dir)
            file_list = "\n".join([os.path.basename(f) for f in saved_files])
            messagebox.showinfo("成功", f"已生成 {len(saved_files)} 个PDF文件:\n{file_list}")
        except Exception as e:
            messagebox.showerror("错误", f"生成PDF报告失败: {e}")
            raise

    def _generate_single_pdf_report(self, model_name, model, X_test, y_test, X_train, y_train, output_path):
        """生成包含所有图表的单个PDF报告"""
        from matplotlib.backends.backend_pdf import PdfPages
        import matplotlib.pyplot as plt
        from datetime import datetime

        # 图表类型列表
        chart_types = ["ROC曲线", "混淆矩阵", "特征重要性", "性能比较", "SHAP分析"]
        if X_train is not None and y_train is not None:
            chart_types.insert(-1, "学习曲线")  # 在SHAP分析前插入学习曲线

        self.gui.log_message(f"开始生成 {model_name} 的完整图表PDF报告...")

        with PdfPages(output_path) as pdf:
            # 第一页：封面
            self._create_pdf_cover_page(pdf, model_name)

            # 生成各类图表
            for i, chart_type in enumerate(chart_types):
                try:
                    self.gui.log_message(f"生成图表 {i+1}/{len(chart_types)}: {chart_type}")

                    # 创建图表
                    if CHART_OPTIMIZATION_AVAILABLE:
                        fig, ax = create_optimized_figure(figsize=(12, 8))
                    else:
                        fig, ax = plt.subplots(figsize=(12, 8))

                    # 根据图表类型绘制
                    success = self._plot_chart_by_type(ax, chart_type, model, X_test, y_test, X_train, y_train, model_name)

                    if success:
                        # 优化图表布局
                        fig.tight_layout(pad=3.0)
                        # 添加到PDF - 保留原始图层的参数
                        pdf.savefig(fig,
                                   bbox_inches='tight',
                                   dpi=300,
                                   facecolor='white',
                                   edgecolor='none',
                                   transparent=False,
                                   pad_inches=0.1)
                    else:
                        # 创建错误页面
                        self._create_error_page(fig, chart_type, "图表生成失败")
                        pdf.savefig(fig,
                                   bbox_inches='tight',
                                   facecolor='white',
                                   edgecolor='none',
                                   transparent=False,
                                   pad_inches=0.1)

                    plt.close(fig)

                except Exception as e:
                    self.gui.log_message(f"生成 {chart_type} 失败: {e}")
                    # 创建错误页面
                    fig, ax = plt.subplots(figsize=(12, 8))
                    self._create_error_page(fig, chart_type, str(e))
                    pdf.savefig(fig,
                               bbox_inches='tight',
                               facecolor='white',
                               edgecolor='none',
                               transparent=False,
                               pad_inches=0.1)
                    plt.close(fig)

            # 最后一页：模型性能摘要
            self._create_pdf_summary_page(pdf, model_name, model, X_test, y_test)

        self.gui.log_message(f"单个PDF报告生成完成: {output_path}")

    def _generate_separate_pdf_reports(self, model_name, model, X_test, y_test, X_train, y_train, save_dir):
        """生成分别的PDF报告文件"""
        from matplotlib.backends.backend_pdf import PdfPages
        import matplotlib.pyplot as plt
        from datetime import datetime

        # 图表类型列表
        chart_types = ["ROC曲线", "混淆矩阵", "特征重要性", "性能比较", "SHAP分析"]
        if X_train is not None and y_train is not None:
            chart_types.insert(-1, "学习曲线")

        saved_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        self.gui.log_message(f"开始生成 {model_name} 的分别PDF报告...")

        # 为每个图表类型生成单独的PDF
        for i, chart_type in enumerate(chart_types):
            try:
                self.gui.log_message(f"生成图表 {i+1}/{len(chart_types)}: {chart_type}")

                # 创建文件名
                safe_chart_name = chart_type.replace("/", "_").replace("\\", "_")
                filename = f"{model_name}_{safe_chart_name}_{timestamp}.pdf"
                output_path = os.path.join(save_dir, filename)

                with PdfPages(output_path) as pdf:
                    # 创建图表
                    if CHART_OPTIMIZATION_AVAILABLE:
                        fig, ax = create_optimized_figure(figsize=(12, 8))
                    else:
                        fig, ax = plt.subplots(figsize=(12, 8))

                    # 根据图表类型绘制
                    success = self._plot_chart_by_type(ax, chart_type, model, X_test, y_test, X_train, y_train, model_name)

                    if success:
                        # 优化图表布局
                        fig.tight_layout(pad=3.0)
                    else:
                        # 创建错误页面
                        self._create_error_page(fig, chart_type, "图表生成失败")

                    # 保存到PDF - 保留原始图层的参数
                    pdf.savefig(fig,
                               bbox_inches='tight',
                               dpi=300,
                               facecolor='white',
                               edgecolor='none',
                               transparent=False,
                               pad_inches=0.1)
                    plt.close(fig)

                saved_files.append(output_path)

            except Exception as e:
                self.gui.log_message(f"生成 {chart_type} PDF失败: {e}")
                # 仍然创建一个错误PDF
                try:
                    safe_chart_name = chart_type.replace("/", "_").replace("\\", "_")
                    filename = f"{model_name}_{safe_chart_name}_{timestamp}_ERROR.pdf"
                    output_path = os.path.join(save_dir, filename)

                    with PdfPages(output_path) as pdf:
                        fig, ax = plt.subplots(figsize=(12, 8))
                        self._create_error_page(fig, chart_type, str(e))
                        pdf.savefig(fig, bbox_inches='tight')
                        plt.close(fig)

                    saved_files.append(output_path)
                except:
                    pass  # 如果连错误PDF都无法创建，就跳过

        self.gui.log_message(f"分别PDF报告生成完成，共 {len(saved_files)} 个文件")
        return saved_files

    def _plot_chart_by_type(self, ax, chart_type, model, X_test, y_test, X_train, y_train, model_name):
        """根据图表类型绘制图表"""
        try:
            if chart_type == "ROC曲线":
                self._plot_roc_curve(ax, model, X_test, y_test, model_name)
            elif chart_type == "混淆矩阵":
                self._plot_confusion_matrix(ax, model, X_test, y_test, model_name)
            elif chart_type == "特征重要性":
                self._plot_feature_importance(ax, model, model_name, X_test)
            elif chart_type == "学习曲线":
                if X_train is not None and y_train is not None:
                    self._plot_learning_curve(ax, model, X_train, y_train, model_name)
                else:
                    ax.text(0.5, 0.5, "学习曲线需要训练数据",
                           ha='center', va='center', transform=ax.transAxes, fontsize=14)
                    return False
            elif chart_type == "性能比较":
                self._plot_performance_metrics(ax, model, X_test, y_test, model_name)
            elif chart_type == "SHAP分析":
                self._plot_shap_analysis(ax, model, X_test, model_name)
            else:
                ax.text(0.5, 0.5, f"未实现的图表类型: {chart_type}",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                return False
            return True
        except Exception as e:
            ax.text(0.5, 0.5, f"绘制失败: {str(e)}",
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            return False

    def _create_error_page(self, fig, chart_type, error_msg):
        """创建错误页面"""
        ax = fig.gca()
        ax.clear()
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

        ax.text(0.5, 0.6, "图表生成失败",
               ha='center', va='center', fontsize=16, fontweight='bold', color='red')
        ax.text(0.5, 0.5, f"图表类型: {chart_type}",
               ha='center', va='center', fontsize=14)
        ax.text(0.5, 0.4, f"错误信息: {error_msg}",
               ha='center', va='center', fontsize=12,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))

    def _create_pdf_cover_page(self, pdf, model_name):
        """创建PDF封面页"""
        from datetime import datetime

        # 设置中文字体
        self._setup_chinese_font()

        fig, ax = plt.subplots(figsize=(12, 8))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

        # 标题
        ax.text(0.5, 0.8, f"{model_name} Model Analysis Report",
               ha='center', va='center', fontsize=24, fontweight='bold',
               bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))

        # 副标题
        ax.text(0.5, 0.65, "Machine Learning Model Analysis",
               ha='center', va='center', fontsize=16, style='italic')

        # 生成时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ax.text(0.5, 0.4, f"Generated: {current_time}",
               ha='center', va='center', fontsize=12)

        # 系统信息
        ax.text(0.5, 0.3, "Multi-Model ML Analysis System",
               ha='center', va='center', fontsize=14, fontweight='bold')

        # 版权信息
        ax.text(0.5, 0.1, "© 2025 Multi-Model ML Analysis System",
               ha='center', va='center', fontsize=10, alpha=0.7)

        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _setup_chinese_font(self):
        """设置字体支持 - 强制使用英文字体避免显示问题"""
        try:
            # 强制使用英文字体，避免中文字符显示为方块
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.family'] = 'sans-serif'
        except Exception:
            # 字体设置失败，使用默认设置
            pass

    def _create_pdf_summary_page(self, pdf, model_name, model, X_test, y_test):
        """创建PDF摘要页"""
        from datetime import datetime
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

        # 设置字体
        self._setup_chinese_font()

        fig, ax = plt.subplots(figsize=(12, 8))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

        # 标题
        ax.text(0.5, 0.9, f"{model_name} Model Performance Summary",
               ha='center', va='center', fontsize=20, fontweight='bold')

        try:
            # 计算性能指标
            y_pred = model.predict(X_test)
            y_pred_proba = None

            if hasattr(model, 'predict_proba'):
                try:
                    y_pred_proba = model.predict_proba(X_test)[:, 1]
                except:
                    pass

            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

            auc_score = None
            if y_pred_proba is not None:
                try:
                    auc_score = roc_auc_score(y_test, y_pred_proba)
                except:
                    pass

            # 显示性能指标
            y_pos = 0.75
            metrics_text = [
                f"Accuracy: {accuracy:.4f}",
                f"Precision: {precision:.4f}",
                f"Recall: {recall:.4f}",
                f"F1-Score: {f1:.4f}"
            ]

            if auc_score is not None:
                metrics_text.append(f"AUC Score: {auc_score:.4f}")

            for i, text in enumerate(metrics_text):
                ax.text(0.5, y_pos - i*0.08, text,
                       ha='center', va='center', fontsize=14,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.6))

            # 数据集信息
            ax.text(0.5, 0.25, f"Test Samples: {len(X_test)}",
                   ha='center', va='center', fontsize=12)
            ax.text(0.5, 0.2, f"Features: {X_test.shape[1]}",
                   ha='center', va='center', fontsize=12)

        except Exception as e:
            ax.text(0.5, 0.5, f"Cannot calculate metrics: {str(e)}",
                   ha='center', va='center', fontsize=14, color='red',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))

        # 生成时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ax.text(0.5, 0.05, f"Report Generated: {current_time}",
               ha='center', va='center', fontsize=10, alpha=0.7)

        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _generate_model_charts_pdf(self, model_name, model, X_test, y_test, X_train, y_train, output_path):
        """生成模型的完整图表PDF报告"""
        from matplotlib.backends.backend_pdf import PdfPages
        import matplotlib.pyplot as plt
        from datetime import datetime

        # 图表类型列表
        chart_types = ["ROC曲线", "混淆矩阵", "特征重要性", "性能比较", "SHAP分析"]
        if X_train is not None and y_train is not None:
            chart_types.insert(-1, "学习曲线")  # 在SHAP分析前插入学习曲线

        self.gui.log_message(f"开始生成 {model_name} 的完整图表PDF报告...")

        with PdfPages(output_path) as pdf:
            # 第一页：封面
            self._create_pdf_cover_page(pdf, model_name)

            # 生成各类图表
            for i, chart_type in enumerate(chart_types):
                try:
                    self.gui.log_message(f"生成图表 {i+1}/{len(chart_types)}: {chart_type}")

                    # 创建图表
                    if CHART_OPTIMIZATION_AVAILABLE:
                        fig, ax = create_optimized_figure(figsize=(12, 8))
                    else:
                        fig, ax = plt.subplots(figsize=(12, 8))

                    # 根据图表类型绘制
                    if chart_type == "ROC曲线":
                        self._plot_roc_curve(ax, model, X_test, y_test, model_name)
                    elif chart_type == "混淆矩阵":
                        self._plot_confusion_matrix(ax, model, X_test, y_test, model_name)
                    elif chart_type == "特征重要性":
                        self._plot_feature_importance(ax, model, model_name, X_test)
                    elif chart_type == "学习曲线":
                        self._plot_learning_curve(ax, model, X_train, y_train, model_name)
                    elif chart_type == "性能比较":
                        self._plot_performance_metrics(ax, model, X_test, y_test, model_name)
                    elif chart_type == "SHAP分析":
                        self._plot_shap_analysis(ax, model, X_test, model_name)

                    # 优化图表布局
                    fig.tight_layout(pad=3.0)

                    # 添加到PDF
                    pdf.savefig(fig, bbox_inches='tight', dpi=300)
                    plt.close(fig)

                except Exception as e:
                    self.gui.log_message(f"生成 {chart_type} 失败: {e}")
                    # 创建错误页面
                    fig, ax = plt.subplots(figsize=(12, 8))
                    ax.text(0.5, 0.5, f"图表生成失败: {chart_type}\n错误: {str(e)}",
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=14, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
                    ax.set_xlim(0, 1)
                    ax.set_ylim(0, 1)
                    ax.axis('off')
                    pdf.savefig(fig, bbox_inches='tight')
                    plt.close(fig)

            # 最后一页：模型性能摘要
            self._create_pdf_summary_page(pdf, model_name, model, X_test, y_test)

        self.gui.log_message(f"PDF报告生成完成: {output_path}")



    def generate_detailed_report(self):
        """生成详细的单模型性能报告"""
        selected_model = self.gui.viz_model_var.get()
        if not selected_model:
            messagebox.showwarning("警告", "请先选择一个模型")
            return

        # 尝试从缓存加载模型数据
        model_data = self._load_model_from_cache(selected_model)

        if model_data is None and selected_model not in self.trained_models:
            messagebox.showwarning("警告", "请先训练模型或选择有效的模型")
            return

        if model_data is None:
            model_data = self.trained_models[selected_model]

        try:
            # 创建报告窗口
            report_window = tk.Toplevel(self.gui.root)
            report_window.title(f"详细性能报告 - {selected_model}")
            report_window.geometry("800x600")
            report_window.transient(self.gui.root)

            # 创建文本显示区域
            text_frame = ttk.Frame(report_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            report_text = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=report_text.yview)
            report_text.configure(yscrollcommand=scrollbar.set)

            report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 生成报告内容
            model_data = self.trained_models[selected_model]
            model = model_data['model']
            X_test = model_data['X_test']
            y_test = model_data['y_test']

            report_content = self._generate_model_report_content(selected_model, model, X_test, y_test)

            # 显示报告
            report_text.insert(tk.END, report_content)
            report_text.config(state=tk.DISABLED)

            # 添加保存按钮
            button_frame = ttk.Frame(report_window)
            button_frame.pack(fill=tk.X, padx=10, pady=5)

            def save_report():
                file_path = filedialog.asksaveasfilename(
                    title="保存报告",
                    defaultextension=".txt",
                    filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
                )
                if file_path:
                    try:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(report_content)
                        messagebox.showinfo("成功", f"报告已保存到: {file_path}")
                    except Exception as e:
                        messagebox.showerror("错误", f"保存报告失败: {e}")

            ttk.Button(button_frame, text="保存报告", command=save_report).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="关闭", command=report_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {e}")

    def _generate_model_report_content(self, model_name, model, X_test, y_test):
        """生成模型报告内容"""
        from sklearn.metrics import (
            accuracy_score, precision_score, recall_score, f1_score,
            roc_auc_score, confusion_matrix, classification_report
        )
        from datetime import datetime

        # 计算预测结果
        y_pred = model.predict(X_test)

        # 计算各项指标
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)

        # 计算AUC
        auc = 0.5
        if hasattr(model, 'predict_proba'):
            try:
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_test, y_pred_proba)
            except:
                pass

        # 计算混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        tn, fp, fn, tp = cm.ravel()

        # 计算特异性
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        sensitivity = recall  # 敏感性就是召回率

        # 生成报告内容
        report = f"""
═══════════════════════════════════════════════════════════════
                    模型性能详细报告
═══════════════════════════════════════════════════════════════

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
模型名称: {model_name}
模型类型: {type(model).__name__}

═══════════════════════════════════════════════════════════════
                    数据集信息
═══════════════════════════════════════════════════════════════

测试集样本数: {len(y_test)}
特征数量: {X_test.shape[1]}
正样本数量: {np.sum(y_test == 1)}
负样本数量: {np.sum(y_test == 0)}
类别平衡比例: {np.sum(y_test == 1) / len(y_test):.3f} : {np.sum(y_test == 0) / len(y_test):.3f}

═══════════════════════════════════════════════════════════════
                    核心性能指标
═══════════════════════════════════════════════════════════════

准确率 (Accuracy):     {accuracy:.4f}
精确率 (Precision):    {precision:.4f}
召回率 (Recall):       {recall:.4f}
F1分数 (F1-Score):     {f1:.4f}
AUC分数:              {auc:.4f}
特异性 (Specificity):  {specificity:.4f}
敏感性 (Sensitivity):  {sensitivity:.4f}

═══════════════════════════════════════════════════════════════
                    混淆矩阵分析
═══════════════════════════════════════════════════════════════

混淆矩阵:
                预测值
实际值    负类    正类
  负类    {tn:4d}    {fp:4d}
  正类    {fn:4d}    {tp:4d}

真负例 (TN): {tn}
假正例 (FP): {fp}
假负例 (FN): {fn}
真正例 (TP): {tp}

═══════════════════════════════════════════════════════════════
                    性能评估
═══════════════════════════════════════════════════════════════

模型整体表现: {self._evaluate_model_performance(accuracy, precision, recall, f1, auc)}

准确率评估: {self._evaluate_metric(accuracy, "accuracy")}
精确率评估: {self._evaluate_metric(precision, "precision")}
召回率评估: {self._evaluate_metric(recall, "recall")}
F1分数评估: {self._evaluate_metric(f1, "f1")}
AUC评估: {self._evaluate_metric(auc, "auc")}

═══════════════════════════════════════════════════════════════
                    改进建议
═══════════════════════════════════════════════════════════════

{self._generate_improvement_suggestions(accuracy, precision, recall, f1, auc, tp, fp, fn, tn)}

═══════════════════════════════════════════════════════════════
                    详细分类报告
═══════════════════════════════════════════════════════════════

{classification_report(y_test, y_pred, target_names=['负类', '正类'])}

═══════════════════════════════════════════════════════════════
                    模型参数信息
═══════════════════════════════════════════════════════════════

{self._get_model_parameters(model)}

═══════════════════════════════════════════════════════════════
                    报告结束
═══════════════════════════════════════════════════════════════
"""
        return report

    def _evaluate_model_performance(self, accuracy, precision, recall, f1, auc):
        """评估模型整体性能"""
        score = (accuracy + precision + recall + f1 + auc) / 5

        if score >= 0.9:
            return "优秀 - 模型性能非常好，可以投入生产使用"
        elif score >= 0.8:
            return "良好 - 模型性能较好，可考虑进一步优化后使用"
        elif score >= 0.7:
            return "中等 - 模型性能一般，建议进行优化"
        elif score >= 0.6:
            return "较差 - 模型性能不佳，需要重新设计"
        else:
            return "很差 - 模型性能很差，建议重新选择算法或特征"

    def _evaluate_metric(self, value, metric_type):
        """评估单个指标"""
        if metric_type == "auc":
            if value >= 0.9:
                return "优秀"
            elif value >= 0.8:
                return "良好"
            elif value >= 0.7:
                return "中等"
            elif value >= 0.6:
                return "较差"
            else:
                return "很差"
        else:
            if value >= 0.95:
                return "优秀"
            elif value >= 0.85:
                return "良好"
            elif value >= 0.75:
                return "中等"
            elif value >= 0.65:
                return "较差"
            else:
                return "很差"

    def _generate_improvement_suggestions(self, accuracy, precision, recall, f1, auc, tp, fp, fn, tn):
        """生成改进建议"""
        suggestions = []

        # 基于整体性能的建议
        if accuracy < 0.8:
            suggestions.append("• 准确率较低，考虑增加更多相关特征或使用更复杂的模型")

        if precision < 0.8:
            suggestions.append("• 精确率较低，存在较多假正例，考虑调整分类阈值或使用成本敏感学习")

        if recall < 0.8:
            suggestions.append("• 召回率较低，存在较多假负例，考虑使用过采样技术或调整类别权重")

        if f1 < 0.8:
            suggestions.append("• F1分数较低，精确率和召回率需要平衡，考虑使用集成方法")

        if auc < 0.8:
            suggestions.append("• AUC较低，模型区分能力不强，考虑特征工程或模型选择")

        # 基于混淆矩阵的建议
        if fp > fn:
            suggestions.append("• 假正例多于假负例，模型倾向于过度预测正类，考虑提高分类阈值")
        elif fn > fp:
            suggestions.append("• 假负例多于假正例，模型倾向于保守预测，考虑降低分类阈值")

        # 数据平衡性建议
        total = tp + fp + fn + tn
        pos_ratio = (tp + fn) / total
        if pos_ratio < 0.1 or pos_ratio > 0.9:
            suggestions.append("• 数据集不平衡，考虑使用SMOTE、欠采样或调整类别权重")

        if not suggestions:
            suggestions.append("• 模型性能良好，可考虑进行超参数调优以进一步提升性能")

        return "\n".join(suggestions)

    def _get_model_parameters(self, model):
        """获取模型参数信息"""
        try:
            params = model.get_params()
            param_str = ""
            for key, value in params.items():
                param_str += f"{key}: {value}\n"
            return param_str
        except:
            return "无法获取模型参数信息"

    def select_best_model_automatically(self):
        """自动选择最佳模型（优化版，支持动态布局和滚动）"""
        if not self.trained_models:
            messagebox.showwarning("警告", "请先训练模型")
            return

        try:
            from best_model_selector import select_best_model_for_binary_classification

            # 创建优化的策略选择窗口
            strategy_window = tk.Toplevel(self.gui.root)
            strategy_window.title("智能模型选择器")

            # 设置窗口大小和位置
            window_width = 600
            window_height = 500
            screen_width = strategy_window.winfo_screenwidth()
            screen_height = strategy_window.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            strategy_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

            # 设置最小尺寸
            strategy_window.minsize(500, 400)
            strategy_window.transient(self.gui.root)
            strategy_window.grab_set()

            # 创建主框架
            main_frame = ttk.Frame(strategy_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(main_frame, text="🎯 智能模型选择器",
                                   font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))

            # 策略选择区域
            strategy_frame = ttk.LabelFrame(main_frame, text="选择策略", padding=10)
            strategy_frame.pack(fill=tk.X, pady=(0, 10))

            strategy_var = tk.StringVar(value="balanced")
            strategies = [
                ("balanced", "平衡策略", "综合考虑性能和稳健性"),
                ("performance", "性能优先", "追求最高预测性能"),
                ("robustness", "稳健性优先", "注重模型稳定性"),
                ("interpretability", "可解释性优先", "便于理解和解释")
            ]

            # 使用网格布局策略选项
            for i, (value, title, desc) in enumerate(strategies):
                row = i // 2
                col = i % 2

                strategy_subframe = ttk.Frame(strategy_frame)
                strategy_subframe.grid(row=row, column=col, sticky="ew", padx=5, pady=2)

                ttk.Radiobutton(strategy_subframe, text=title, variable=strategy_var,
                               value=value, width=15).pack(anchor=tk.W)
                ttk.Label(strategy_subframe, text=desc, font=("Arial", 8),
                         foreground="gray").pack(anchor=tk.W, padx=(20, 0))

            # 配置网格权重
            strategy_frame.grid_columnconfigure(0, weight=1)
            strategy_frame.grid_columnconfigure(1, weight=1)

            # 结果显示区域（带滚动条）
            result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding=5)
            result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 创建带滚动条的文本区域
            text_container = ttk.Frame(result_frame)
            text_container.pack(fill=tk.BOTH, expand=True)

            result_text = tk.Text(text_container, wrap=tk.WORD, font=("Consolas", 9))
            result_scrollbar = ttk.Scrollbar(text_container, orient=tk.VERTICAL,
                                           command=result_text.yview)
            result_text.configure(yscrollcommand=result_scrollbar.set)

            result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 初始提示信息
            result_text.insert(tk.END, "💡 请选择策略后点击'开始分析'按钮\n\n")
            result_text.insert(tk.END, "策略说明:\n")
            result_text.insert(tk.END, "• 平衡策略: 综合评估准确率、AUC、稳定性等指标\n")
            result_text.insert(tk.END, "• 性能优先: 主要关注AUC和准确率\n")
            result_text.insert(tk.END, "• 稳健性优先: 重视交叉验证稳定性\n")
            result_text.insert(tk.END, "• 可解释性优先: 偏向线性模型和树模型\n")

            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            def run_selection():
                strategy = strategy_var.get()
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, "🔄 正在分析模型性能，请稍候...\n")
                result_text.insert(tk.END, f"选择策略: {strategy}\n")
                result_text.insert(tk.END, f"分析模型数量: {len(self.trained_models)}\n\n")
                strategy_window.update()

                try:
                    # 运行最佳模型选择
                    selection_result = select_best_model_for_binary_classification(
                        strategy=strategy, top_k=5
                    )

                    if selection_result:
                        # 显示结果
                        result_text.delete(1.0, tk.END)
                        result_text.insert(tk.END, "🎯 最佳模型推荐结果\n")
                        result_text.insert(tk.END, "=" * 50 + "\n\n")

                        result_text.insert(tk.END, f"📊 推荐策略: {strategy}\n")
                        result_text.insert(tk.END, f"🏆 最佳模型: {selection_result['best_model']}\n")
                        result_text.insert(tk.END, f"⭐ 综合得分: {selection_result['best_score']:.4f}\n\n")

                        result_text.insert(tk.END, "📈 前五名模型排名:\n")
                        result_text.insert(tk.END, "-" * 30 + "\n")
                        for i, (model, score) in enumerate(selection_result['top_models'], 1):
                            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                            result_text.insert(tk.END, f"{medal} {model}: {score:.4f}\n")

                        result_text.insert(tk.END, "\n💡 推荐理由:\n")
                        result_text.insert(tk.END, "-" * 30 + "\n")
                        result_text.insert(tk.END, selection_result['selection_reasoning'])

                        # 更新GUI中的模型选择
                        self.gui.viz_model_var.set(selection_result['best_model'])

                        result_text.insert(tk.END, "\n\n✅ 已自动选择最佳模型用于后续分析")

                    else:
                        result_text.insert(tk.END, "❌ 模型选择失败，请检查训练结果")

                except Exception as e:
                    result_text.insert(tk.END, f"❌ 分析过程出错: {e}")
                    import traceback
                    result_text.insert(tk.END, f"\n\n详细错误信息:\n{traceback.format_exc()}")

            # 按钮布局
            ttk.Button(button_frame, text="🚀 开始分析", command=run_selection).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="❌ 关闭", command=strategy_window.destroy).pack(side=tk.RIGHT)

            # 添加帮助按钮
            def show_help():
                help_text = """
智能模型选择器使用说明:

1. 策略选择:
   • 平衡策略: 适合大多数场景，综合考虑各项指标
   • 性能优先: 适合对准确率要求极高的场景
   • 稳健性优先: 适合对模型稳定性要求高的场景
   • 可解释性优先: 适合需要解释模型决策的场景

2. 分析过程:
   • 系统会自动评估所有已训练的模型
   • 根据选择的策略计算综合得分
   • 推荐最适合的模型并给出理由

3. 结果应用:
   • 最佳模型会自动设置为当前可视化模型
   • 可以直接进行后续的分析和可视化
                """
                messagebox.showinfo("使用帮助", help_text)

            ttk.Button(button_frame, text="❓ 帮助", command=show_help).pack(side=tk.RIGHT, padx=(0, 10))

            # 绑定窗口关闭事件
            def on_closing():
                strategy_window.destroy()

            strategy_window.protocol("WM_DELETE_WINDOW", on_closing)

            # 设置焦点
            strategy_window.focus_set()

        except ImportError:
            messagebox.showerror("错误", "无法导入最佳模型选择模块")
        except Exception as e:
            messagebox.showerror("错误", f"自动选择最佳模型失败: {e}")
            import traceback
            print(traceback.format_exc())

    def run_complete_analysis(self):
        """运行完整的二分类分析流程"""
        if not self.gui.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_models = self.gui.get_selected_models()
        if not selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return

        try:
            from binary_classification_pipeline import run_binary_classification_analysis

            # 创建分析配置窗口
            config_window = tk.Toplevel(self.gui.root)
            config_window.title("完整分析配置")
            config_window.geometry("500x400")
            config_window.transient(self.gui.root)
            config_window.grab_set()

            # 配置选项
            ttk.Label(config_window, text="完整二分类分析配置", font=("Arial", 14, "bold")).pack(pady=10)

            # 模型选择策略
            strategy_frame = ttk.LabelFrame(config_window, text="模型选择策略")
            strategy_frame.pack(fill=tk.X, padx=10, pady=5)

            strategy_var = tk.StringVar(value="balanced")
            strategies = [
                ("balanced", "平衡策略"),
                ("performance", "性能优先"),
                ("robustness", "稳健性优先"),
                ("interpretability", "可解释性优先")
            ]

            for value, text in strategies:
                ttk.Radiobutton(strategy_frame, text=text, variable=strategy_var,
                               value=value).pack(anchor=tk.W, padx=10, pady=2)

            # 分析选项
            options_frame = ttk.LabelFrame(config_window, text="分析选项")
            options_frame.pack(fill=tk.X, padx=10, pady=5)

            enable_tuning_var = tk.BooleanVar(value=True)
            enable_shap_var = tk.BooleanVar(value=True)

            ttk.Checkbutton(options_frame, text="启用超参数调优",
                           variable=enable_tuning_var).pack(anchor=tk.W, padx=10, pady=2)
            ttk.Checkbutton(options_frame, text="启用SHAP可解释性分析",
                           variable=enable_shap_var).pack(anchor=tk.W, padx=10, pady=2)

            # 进度显示
            progress_frame = ttk.LabelFrame(config_window, text="分析进度")
            progress_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            progress_text = tk.Text(progress_frame, height=10)
            progress_scrollbar = ttk.Scrollbar(progress_frame, orient=tk.VERTICAL, command=progress_text.yview)
            progress_text.configure(yscrollcommand=progress_scrollbar.set)

            progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            progress_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            def run_analysis():
                strategy = strategy_var.get()
                enable_tuning = enable_tuning_var.get()
                enable_shap = enable_shap_var.get()

                progress_text.delete(1.0, tk.END)
                progress_text.insert(tk.END, "🚀 开始完整的二分类分析流程...\n")
                progress_text.insert(tk.END, f"数据文件: {self.gui.current_data_path.get()}\n")
                progress_text.insert(tk.END, f"选择的模型: {', '.join(selected_models)}\n")
                progress_text.insert(tk.END, f"选择策略: {strategy}\n")
                progress_text.insert(tk.END, f"超参数调优: {'启用' if enable_tuning else '禁用'}\n")
                progress_text.insert(tk.END, f"SHAP分析: {'启用' if enable_shap else '禁用'}\n")
                progress_text.insert(tk.END, "="*50 + "\n")
                config_window.update()

                try:
                    # 在新线程中运行分析
                    def analysis_thread():
                        success = run_binary_classification_analysis(
                            data_path=self.gui.current_data_path.get(),
                            selected_models=selected_models,
                            strategy=strategy,
                            enable_tuning=enable_tuning,
                            enable_shap=enable_shap
                        )

                        # 线程安全的GUI更新
                        def update_analysis_result():
                            if success:
                                progress_text.insert(tk.END, "\n🎉 完整分析成功完成！\n")
                                progress_text.insert(tk.END, "请查看输出目录中的详细报告和可视化结果。\n")

                                # 自动加载最佳模型到可视化界面
                                try:
                                    from best_model_selector import BestModelSelector
                                    selector = BestModelSelector(strategy=strategy)
                                    if selector.load_model_results():
                                        result = selector.select_best_model(top_k=1)
                                        if result:
                                            self.gui.viz_model_var.set(result['best_model'])
                                            progress_text.insert(tk.END, f"已自动选择最佳模型: {result['best_model']}\n")
                                except:
                                    pass
                            else:
                                progress_text.insert(tk.END, "\n❌ 分析过程中出现错误！\n")

                        config_window.after(0, update_analysis_result)

                    import threading
                    thread = threading.Thread(target=analysis_thread)
                    thread.daemon = True
                    thread.start()

                except Exception as e:
                    progress_text.insert(tk.END, f"\n❌ 启动分析失败: {e}\n")

            # 按钮
            button_frame = ttk.Frame(config_window)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(button_frame, text="开始分析", command=run_analysis).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="关闭", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

        except ImportError:
            messagebox.showerror("错误", "无法导入完整分析流程模块")
        except Exception as e:
            messagebox.showerror("错误", f"启动完整分析失败: {e}")

    def start_hyperparameter_tuning(self):
        """开始超参数调优"""
        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中，请等待完成")
            return

        # 检查数据是否已加载
        if not self.gui.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        # 检查是否选择了模型
        selected_models = self.gui.get_selected_models()
        if not selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return

        # 获取调优配置
        tuning_config = self.gui.get_tuning_config()
        if not tuning_config['enabled']:
            messagebox.showwarning("警告", "请先启用超参数调优")
            return

        # 在新线程中执行调优
        self.tuning_thread = threading.Thread(
            target=self._hyperparameter_tuning_thread,
            args=(selected_models, tuning_config)
        )
        self.tuning_thread.daemon = True
        self.tuning_thread.start()

    def _hyperparameter_tuning_thread(self, selected_models, tuning_config):
        """在后台线程中执行超参数调优"""
        try:
            self.is_training = True
            self.gui.status_text.set("正在进行超参数调优...")
            self.gui.log_message("开始超参数调优")

            # 应用严格复现模式设置（与CLI保持一致）
            try:
                repro_config = self.gui.get_reproducibility_config()
                if repro_config['strict_reproducibility']:
                    from config import REPRODUCIBILITY_CONFIG, apply_reproducibility_env, set_global_seed, RANDOM_SEED

                    # 更新全局配置
                    REPRODUCIBILITY_CONFIG['strict'] = True
                    REPRODUCIBILITY_CONFIG['num_threads'] = repro_config['num_threads']
                    REPRODUCIBILITY_CONFIG['enforce_cpu'] = repro_config['enforce_cpu']

                    # 应用环境设置
                    apply_reproducibility_env(strict=True)

                    # 重新设置全局随机种子
                    set_global_seed(RANDOM_SEED)

                    self.gui.log_message(f"超参数调优启用严格复现模式 (线程数: {repro_config['num_threads']}, 强制CPU: {repro_config['enforce_cpu']})")
                else:
                    self.gui.log_message("超参数调优使用标准模式")
            except Exception as e:
                self.gui.log_message(f"应用严格复现模式设置失败: {e}")

            # 导入调优模块
            try:
                from hyperparameter_tuning import tune_model
            except ImportError:
                self.gui.log_message("无法导入超参数调优模块")
                messagebox.showerror("错误", "无法导入超参数调优模块")
                return

            # 准备数据
            data_path = self.gui.current_data_path.get()
            test_size = self.gui.test_size_var.get()
            random_seed = self.gui.random_seed_var.get()
            scaling_method = self.gui.scaling_var.get()

            # 创建数据预处理器
            scaling_method_param = scaling_method if scaling_method != "none" else "standard"
            preprocessor = DataPreprocessor(
                test_size=test_size,
                random_state=random_seed,
                scaling_method=scaling_method_param
            )

            # 加载和预处理数据
            X_train, X_test, y_train, y_test = preprocessor.load_and_preprocess(data_path)

            # 存储调优结果
            self.tuning_results = {}

            # 对每个选中的模型进行调优
            total_models = len(selected_models)
            for i, model_name in enumerate(selected_models):
                if not self.is_training:  # 检查是否被停止
                    break

                self.gui.log_message(f"正在调优 {model_name}...")
                progress = (i / total_models) * 100
                self.gui.training_progress.set(progress)

                try:
                    # 执行超参数调优，获取study对象用于可视化
                    early_stopping_rounds = tuning_config.get('early_stopping', False)
                    tune_result = tune_model(
                        model_name=model_name,
                        n_trials=tuning_config['n_trials'],
                        X_train=X_train,
                        y_train=y_train,
                        return_study=True,
                        early_stopping_rounds=early_stopping_rounds,
                        patience=tuning_config.get('patience', 10),
                        min_improvement=tuning_config.get('min_improvement', 0.001),
                        strategy=tuning_config.get('strategy', 'TPE'),
                        timeout=tuning_config.get('timeout', None),
                        n_jobs=tuning_config.get('n_jobs', 1)
                    )

                    # 处理不同的返回值格式
                    if len(tune_result) == 3:
                        best_params, best_score, study = tune_result
                    else:
                        best_params, best_score = tune_result
                        study = None

                    # 保存调优结果
                    self.tuning_results[model_name] = {
                        'best_params': best_params,
                        'best_score': best_score,
                        'config': tuning_config,
                        'study': study,
                        'X_train': X_train,
                        'y_train': y_train
                    }

                    self.gui.log_message(f"{model_name} 调优完成，最佳得分: {best_score:.4f}")

                except Exception as e:
                    self.gui.log_message(f"{model_name} 调优失败: {e}")

            # 调优完成
            self.gui.training_progress.set(100)
            self.gui.status_text.set(f"超参数调优完成，共调优 {len(self.tuning_results)} 个模型")
            self.gui.log_message("所有模型超参数调优完成")

            # 显示调优结果摘要
            self._show_tuning_summary()

        except Exception as e:
            self.gui.log_message(f"超参数调优过程出错: {e}")
            messagebox.showerror("错误", f"超参数调优失败: {e}")
        finally:
            self.is_training = False

    def _show_tuning_summary(self):
        """显示调优结果摘要"""
        if not hasattr(self, 'tuning_results') or not self.tuning_results:
            return

        summary_text = "超参数调优结果摘要:\n" + "="*50 + "\n"

        # 按得分排序
        sorted_results = sorted(
            self.tuning_results.items(),
            key=lambda x: x[1]['best_score'],
            reverse=True
        )

        for i, (model_name, result) in enumerate(sorted_results, 1):
            summary_text += f"\n{i}. {model_name}:\n"
            summary_text += f"   最佳得分: {result['best_score']:.4f}\n"
            summary_text += f"   最佳参数: {result['best_params']}\n"

        self.gui.log_message(summary_text)

    def show_tuning_results(self):
        """显示详细的调优结果"""
        if not hasattr(self, 'tuning_results') or not self.tuning_results:
            messagebox.showwarning("警告", "没有可显示的调优结果，请先进行超参数调优")
            return

        # 创建结果显示窗口
        result_window = tk.Toplevel(self.gui.root)
        result_window.title("超参数调优结果")
        result_window.geometry("800x600")
        result_window.transient(self.gui.root)
        result_window.grab_set()

        # 创建选项卡
        notebook = ttk.Notebook(result_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 结果摘要选项卡
        self._create_summary_tab(notebook)

        # 详细结果选项卡
        self._create_detailed_results_tab(notebook)

        # 参数比较选项卡
        self._create_parameter_comparison_tab(notebook)

        # 可视化选项卡
        self._create_visualization_tab(notebook)

    def _create_summary_tab(self, notebook):
        """创建结果摘要选项卡"""
        summary_frame = ttk.Frame(notebook)
        notebook.add(summary_frame, text="结果摘要")

        # 创建表格显示结果
        columns = ("排名", "模型", "最佳得分", "改进幅度")
        tree = ttk.Treeview(summary_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 填充数据
        sorted_results = sorted(
            self.tuning_results.items(),
            key=lambda x: x[1]['best_score'],
            reverse=True
        )

        baseline_scores = {}  # 这里可以存储基线模型得分进行比较

        for i, (model_name, result) in enumerate(sorted_results, 1):
            best_score = result['best_score']
            # 计算改进幅度（这里简化处理，实际应该与基线模型比较）
            improvement = "N/A"  # 可以后续完善

            tree.insert("", "end", values=(
                i, model_name, f"{best_score:.4f}", improvement
            ))

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_detailed_results_tab(self, notebook):
        """创建详细结果选项卡"""
        detail_frame = ttk.Frame(notebook)
        notebook.add(detail_frame, text="详细结果")

        # 模型选择
        select_frame = ttk.Frame(detail_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(select_frame, text="选择模型:").pack(side=tk.LEFT, padx=5)
        model_var = tk.StringVar()
        model_combo = ttk.Combobox(select_frame, textvariable=model_var,
                                 values=list(self.tuning_results.keys()), state="readonly")
        model_combo.pack(side=tk.LEFT, padx=5)

        # 详细信息显示
        detail_text = tk.Text(detail_frame, wrap=tk.WORD)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=detail_text.yview)
        detail_text.configure(yscrollcommand=detail_scrollbar.set)

        def on_model_select(event):
            selected_model = model_var.get()
            if selected_model and selected_model in self.tuning_results:
                result = self.tuning_results[selected_model]

                detail_info = f"模型: {selected_model}\n"
                detail_info += f"最佳得分: {result['best_score']:.4f}\n\n"
                detail_info += "最佳参数:\n"
                for param, value in result['best_params'].items():
                    detail_info += f"  {param}: {value}\n"

                detail_info += "\n调优配置:\n"
                config = result['config']
                detail_info += f"  试验次数: {config['n_trials']}\n"
                detail_info += f"  调优策略: {config['strategy']}\n"
                detail_info += f"  评估指标: {config['metric']}\n"

                detail_text.delete(1.0, tk.END)
                detail_text.insert(1.0, detail_info)

        model_combo.bind("<<ComboboxSelected>>", on_model_select)

        detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 默认选择第一个模型
        if self.tuning_results:
            model_combo.set(list(self.tuning_results.keys())[0])
            on_model_select(None)

    def _create_parameter_comparison_tab(self, notebook):
        """创建参数比较选项卡"""
        comparison_frame = ttk.Frame(notebook)
        notebook.add(comparison_frame, text="参数比较")

        # 创建参数比较表格
        comparison_text = tk.Text(comparison_frame, wrap=tk.WORD)
        comparison_scrollbar = ttk.Scrollbar(comparison_frame, orient=tk.VERTICAL, command=comparison_text.yview)
        comparison_text.configure(yscrollcommand=comparison_scrollbar.set)

        # 生成参数比较信息
        comparison_info = "模型参数比较:\n" + "="*60 + "\n\n"

        for model_name, result in self.tuning_results.items():
            comparison_info += f"{model_name} (得分: {result['best_score']:.4f}):\n"
            for param, value in result['best_params'].items():
                comparison_info += f"  {param}: {value}\n"
            comparison_info += "\n"

        comparison_text.insert(1.0, comparison_info)
        comparison_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        comparison_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_visualization_tab(self, notebook):
        """创建可视化选项卡"""
        viz_frame = ttk.Frame(notebook)
        notebook.add(viz_frame, text="可视化")

        # 控制面板
        control_frame = ttk.Frame(viz_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模型选择
        ttk.Label(control_frame, text="选择模型:").pack(side=tk.LEFT, padx=5)
        viz_model_var = tk.StringVar()
        viz_model_combo = ttk.Combobox(control_frame, textvariable=viz_model_var,
                                     values=list(self.tuning_results.keys()), state="readonly")
        viz_model_combo.pack(side=tk.LEFT, padx=5)

        # 图表类型选择
        ttk.Label(control_frame, text="图表类型:").pack(side=tk.LEFT, padx=(20, 5))
        chart_type_var = tk.StringVar(value="优化历史")
        chart_combo = ttk.Combobox(control_frame, textvariable=chart_type_var,
                                 values=["优化历史", "参数重要性", "参数关系"], state="readonly")
        chart_combo.pack(side=tk.LEFT, padx=5)

        # 生成图表按钮
        ttk.Button(control_frame, text="生成图表",
                  command=lambda: self._generate_tuning_chart(viz_model_var.get(), chart_type_var.get(), chart_frame)).pack(side=tk.LEFT, padx=10)

        # 图表显示区域
        chart_frame = ttk.Frame(viz_frame)
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 默认选择第一个模型
        if self.tuning_results:
            viz_model_combo.set(list(self.tuning_results.keys())[0])

    def _generate_tuning_chart(self, model_name, chart_type, parent_frame):
        """生成调优图表"""
        if not model_name or model_name not in self.tuning_results:
            messagebox.showwarning("警告", "请选择有效的模型")
            return

        result = self.tuning_results[model_name]
        study = result.get('study')

        if study is None:
            messagebox.showwarning("警告", "该模型没有可用的调优数据")
            return

        # 清除之前的图表
        for widget in parent_frame.winfo_children():
            widget.destroy()

        try:
            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(10, 6))

            if chart_type == "优化历史":
                self._plot_optimization_history(ax, study, model_name)
            elif chart_type == "参数重要性":
                self._plot_parameter_importance(ax, study, model_name)
            elif chart_type == "参数关系":
                self._plot_parameter_relationships(ax, study, model_name)

            # 在GUI中显示图表
            canvas = FigureCanvasTkAgg(fig, parent_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            try:
                from matplotlib.backends._backend_tk import NavigationToolbar2Tk
            except ImportError:
                try:
                    from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
                except ImportError:
                    NavigationToolbar2Tk = None

            if NavigationToolbar2Tk is not None:
                toolbar = NavigationToolbar2Tk(canvas, parent_frame)
                toolbar.update()

        except Exception as e:
            messagebox.showerror("错误", f"生成图表失败: {e}")

    def _plot_optimization_history(self, ax, study, model_name):
        """绘制优化历史"""
        trials = study.trials
        if not trials:
            ax.text(0.5, 0.5, "没有可用的试验数据", ha='center', va='center', transform=ax.transAxes)
            return

        # 提取试验数据
        trial_numbers = [trial.number for trial in trials]
        values = [trial.value if trial.value is not None else 0 for trial in trials]

        # 计算累积最佳值
        best_values = []
        current_best = float('-inf')
        for value in values:
            if value > current_best:
                current_best = value
            best_values.append(current_best)

        # 绘制图表
        ax.plot(trial_numbers, values, 'o-', alpha=0.6, label='试验值')
        ax.plot(trial_numbers, best_values, 'r-', linewidth=2, label='最佳值')

        ax.set_xlabel('试验次数')
        ax.set_ylabel('目标值')
        ax.set_title(f'{model_name} - 优化历史')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_parameter_importance(self, ax, study, model_name):
        """绘制参数重要性"""
        try:
            # 计算参数重要性
            importance = optuna.importance.get_param_importances(study)

            if not importance:
                ax.text(0.5, 0.5, "无法计算参数重要性", ha='center', va='center', transform=ax.transAxes)
                return

            # 准备数据
            params = list(importance.keys())
            importances = list(importance.values())

            # 创建水平条形图
            y_pos = np.arange(len(params))
            ax.barh(y_pos, importances)
            ax.set_yticks(y_pos)
            ax.set_yticklabels(params)
            ax.set_xlabel('重要性')
            ax.set_title(f'{model_name} - 参数重要性')
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f"计算参数重要性失败: {e}", ha='center', va='center', transform=ax.transAxes)

    def _plot_parameter_relationships(self, ax, study, model_name):
        """绘制参数关系"""
        trials = study.trials
        if len(trials) < 2:
            ax.text(0.5, 0.5, "试验数据不足", ha='center', va='center', transform=ax.transAxes)
            return

        # 获取所有参数名
        all_params = set()
        for trial in trials:
            all_params.update(trial.params.keys())

        if len(all_params) < 2:
            ax.text(0.5, 0.5, "参数数量不足", ha='center', va='center', transform=ax.transAxes)
            return

        # 选择前两个参数进行可视化
        param_names = list(all_params)[:2]
        param1_name, param2_name = param_names[0], param_names[1]

        # 提取参数值和目标值
        param1_values = []
        param2_values = []
        objective_values = []

        for trial in trials:
            if param1_name in trial.params and param2_name in trial.params and trial.value is not None:
                param1_values.append(trial.params[param1_name])
                param2_values.append(trial.params[param2_name])
                objective_values.append(trial.value)

        if not param1_values:
            ax.text(0.5, 0.5, "没有有效的参数数据", ha='center', va='center', transform=ax.transAxes)
            return

        # 创建散点图
        scatter = ax.scatter(param1_values, param2_values, c=objective_values, cmap='viridis', alpha=0.6)
        ax.set_xlabel(param1_name)
        ax.set_ylabel(param2_name)
        ax.set_title(f'{model_name} - 参数关系 ({param1_name} vs {param2_name})')

        # 添加颜色条
        plt.colorbar(scatter, ax=ax, label='目标值')

    def generate_performance_report(self):
        """生成模型性能比较报告"""
        try:
            # 检查是否有训练好的模型
            from config import CACHE_PATH
            from pathlib import Path

            cache_path = Path(CACHE_PATH)
            if not cache_path.exists():
                messagebox.showwarning("警告", "没有找到模型缓存，请先训练模型")
                return

            # 查找已训练的模型
            model_files = list(cache_path.glob("*_results.joblib"))
            if not model_files:
                messagebox.showwarning("警告", "没有找到已训练的模型，请先训练模型")
                return

            # 创建进度窗口
            progress_window = tk.Toplevel(self.gui.root)
            progress_window.title("生成性能报告")
            progress_window.geometry("600x400")
            progress_window.transient(self.gui.root)
            progress_window.grab_set()

            # 进度显示
            ttk.Label(progress_window, text="正在生成模型性能比较报告...",
                     font=('Arial', 12, 'bold')).pack(pady=10)

            progress_text = tk.Text(progress_window, height=20, width=70)
            progress_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            scrollbar = ttk.Scrollbar(progress_window, orient="vertical", command=progress_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            progress_text.configure(yscrollcommand=scrollbar.set)

            # 创建线程安全的进度更新函数
            update_progress = create_progress_updater(progress_window, progress_text)

            def generate_report_thread():
                try:
                    update_progress("🚀 开始生成模型性能比较报告...")

                    # 确保在后台线程中使用非交互式matplotlib后端
                    safe_matplotlib_backend_switch()

                    # 导入报告生成模块
                    from model_performance_report import generate_comprehensive_report

                    update_progress("📊 正在加载模型结果...")

                    # 检查会话管理并获取要包含的模型列表
                    update_progress("📁 正在检查会话管理...")

                    selected_models_for_report = None
                    current_session = None

                    try:
                        from training_session_manager import get_current_session, get_session_manager
                        current_session = get_current_session()

                        # 如果没有当前会话，尝试激活最新的会话
                        if not current_session:
                            session_manager = get_session_manager()
                            sessions = session_manager.list_sessions()

                            if sessions:
                                latest_session_id = sessions[0]['session_id']
                                if session_manager.set_current_session(latest_session_id):
                                    current_session = get_current_session()
                                    session_name = getattr(current_session, 'session_name', '未知会话')
                                    update_progress(f"✅ 激活会话: {session_name}")

                        # 获取当前会话训练的模型列表
                        if current_session:
                            trained_models = current_session.metadata.get('trained_models', [])
                            if trained_models:
                                # 去重模型名称
                                selected_models_for_report = list(set([model['model_name'] for model in trained_models]))
                                update_progress(f"📊 当前会话包含 {len(selected_models_for_report)} 个模型: {', '.join(selected_models_for_report)}")
                            else:
                                update_progress("⚠️ 当前会话没有训练的模型，将使用所有可用模型")

                    except Exception as e:
                        update_progress(f"⚠️ 会话管理功能异常: {e}")
                        update_progress("📊 将使用所有可用模型生成报告")

                    # 生成报告到默认位置（使用指定的模型列表）
                    generate_comprehensive_report(selected_models=selected_models_for_report)

                    update_progress("📁 正在处理报告文件...")

                    # 尝试复制报告文件到会话目录
                    try:

                        if current_session:
                            # 复制报告文件到会话目录
                            import shutil
                            from config import PROJECT_ROOT

                            session_reports_path = current_session.get_path('reports')
                            default_reports_path = PROJECT_ROOT / 'reports'

                            update_progress(f"📂 复制报告到会话目录: {session_reports_path}")

                            # 复制所有报告文件
                            report_files = [
                                'performance_report.html',
                                'performance_metrics.csv',
                                'performance_data.json',
                                'model_performance_comparison.xlsx',
                                'performance_heatmap.png',
                                'radar_comparison.png',
                                'model_ranking.png'
                            ]

                            copied_files = []
                            for filename in report_files:
                                src_file = default_reports_path / filename
                                dst_file = session_reports_path / filename

                                if src_file.exists():
                                    shutil.copy2(src_file, dst_file)
                                    copied_files.append(filename)

                            update_progress(f"✅ 已复制 {len(copied_files)} 个报告文件到会话目录")

                            # 显示会话目录中的文件位置
                            update_progress("\n✅ 报告生成完成！")
                            update_progress(f"📁 会话报告位置 ({current_session.session_name})：")
                            for filename in copied_files:
                                update_progress(f"   - {filename}: {session_reports_path}/{filename}")

                            # 添加打开报告按钮
                            def open_html_report():
                                try:
                                    import webbrowser
                                    report_path = session_reports_path / 'performance_report.html'
                                    webbrowser.open(f'file:///{report_path}')
                                except Exception as e:
                                    progress_window.after(0, lambda: messagebox.showerror("错误", f"打开报告失败: {e}"))
                        else:
                            # 没有会话，显示默认位置
                            update_progress("\n✅ 报告生成完成！")
                            update_progress("📁 报告文件位置：")
                            update_progress("   - HTML报告: reports/performance_report.html")
                            update_progress("   - CSV数据: reports/performance_metrics.csv")
                            update_progress("   - Excel表格: reports/model_performance_comparison.xlsx")
                            update_progress("   - JSON数据: reports/performance_data.json")
                            update_progress("   - 性能热图: reports/performance_heatmap.png")
                            update_progress("   - 雷达对比图: reports/radar_comparison.png")
                            update_progress("   - 模型排名图: reports/model_ranking.png")

                            # 添加打开报告按钮
                            def open_html_report():
                                try:
                                    import webbrowser
                                    from config import PROJECT_ROOT
                                    report_path = PROJECT_ROOT / 'reports' / 'performance_report.html'
                                    webbrowser.open(f'file:///{report_path}')
                                except Exception as e:
                                    progress_window.after(0, lambda: messagebox.showerror("错误", f"打开报告失败: {e}"))

                    except Exception as e:
                        update_progress(f"⚠️ 会话管理功能异常: {e}")
                        update_progress("📁 报告已保存到默认位置: reports/")

                        # 添加打开报告按钮
                        def open_html_report():
                            try:
                                import webbrowser
                                from config import PROJECT_ROOT
                                report_path = PROJECT_ROOT / 'reports' / 'performance_report.html'
                                webbrowser.open(f'file:///{report_path}')
                            except Exception as e:
                                progress_window.after(0, lambda: messagebox.showerror("错误", f"打开报告失败: {e}"))

                    update_progress("\n💡 您可以打开HTML报告查看详细结果")

                    # 在主线程中添加按钮
                    def add_buttons():
                        button_frame = ttk.Frame(progress_window)
                        button_frame.pack(fill=tk.X, padx=10, pady=5)

                        ttk.Button(button_frame, text="打开HTML报告",
                                  command=open_html_report).pack(side=tk.LEFT, padx=5)
                        ttk.Button(button_frame, text="关闭",
                                  command=progress_window.destroy).pack(side=tk.RIGHT, padx=5)

                    progress_window.after(0, add_buttons)

                except Exception as e:
                    update_progress(f"\n❌ 报告生成失败: {e}")
                    update_progress("请确保已经训练了模型并且缓存文件存在")

            # 在新线程中生成报告
            report_thread = threading.Thread(target=generate_report_thread)
            report_thread.daemon = True
            report_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动报告生成失败: {e}")

    def _perform_delong_test(self):
        """执行DeLong检验比较模型性能"""
        try:
            from delong_test import perform_delong_comparison

            # 准备模型数据
            model_data = {}
            for model_name, model_info in self.trained_models.items():
                trainer = model_info['trainer']
                model = model_info['model']
                X_test = model_info['X_test']
                y_test = model_info['y_test']

                # 获取预测概率
                if hasattr(model, 'predict_proba'):
                    y_score = model.predict_proba(X_test)[:, 1]
                elif hasattr(model, 'decision_function'):
                    y_score = model.decision_function(X_test)
                else:
                    continue  # 跳过无法获取概率的模型

                model_data[model_name] = {
                    'y_true': y_test,
                    'y_score': y_score
                }

            if len(model_data) < 2:
                self.gui.log_message("需要至少2个支持概率预测的模型才能进行DeLong检验")
                return

            # 执行DeLong检验
            comparison_results = perform_delong_comparison(model_data)

            # 显示结果
            self._display_delong_results(comparison_results)

        except ImportError:
            self.gui.log_message("DeLong检验模块未找到，正在创建...")
            # 如果模块不存在，先创建它
            self._create_delong_module()
            # 然后重新尝试
            self._perform_delong_test()
        except Exception as e:
            self.gui.log_message(f"DeLong检验执行失败: {e}")

    def _display_delong_results(self, results):
        """显示DeLong检验结果"""
        import tkinter as tk
        from tkinter import ttk

        # 创建结果显示窗口
        result_window = tk.Toplevel(self.gui.root)
        result_window.title("DeLong检验结果")
        result_window.geometry("800x600")

        # 创建文本框显示结果
        text_frame = ttk.Frame(result_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 格式化并显示结果
        result_text = "🔬 DeLong检验结果 - 模型ROC曲线比较\n"
        result_text += "=" * 60 + "\n\n"

        if 'summary' in results:
            result_text += "📊 总结:\n"
            result_text += f"共比较了 {results['summary']['total_comparisons']} 对模型\n"
            result_text += f"显著差异的比较: {results['summary']['significant_comparisons']}\n\n"

        if 'pairwise_results' in results:
            result_text += "📈 两两比较结果:\n"
            result_text += "-" * 40 + "\n"

            for comparison in results['pairwise_results']:
                model1 = comparison['model1']
                model2 = comparison['model2']
                auc1 = comparison['auc1']
                auc2 = comparison['auc2']
                p_value = comparison['p_value']
                is_significant = comparison['is_significant']

                result_text += f"\n🆚 {model1} vs {model2}:\n"
                result_text += f"   AUC1: {auc1:.4f}\n"
                result_text += f"   AUC2: {auc2:.4f}\n"
                result_text += f"   AUC差异: {abs(auc1 - auc2):.4f}\n"
                result_text += f"   p值: {p_value:.6f}\n"
                result_text += f"   显著性: {'是' if is_significant else '否'} (α=0.05)\n"

                if is_significant:
                    better_model = model1 if auc1 > auc2 else model2
                    result_text += f"   🏆 {better_model} 显著优于另一个模型\n"
                else:
                    result_text += f"   ⚖️ 两个模型性能无显著差异\n"

        result_text += "\n" + "=" * 60 + "\n"
        result_text += "注: DeLong检验用于比较两个ROC曲线的AUC是否存在显著差异\n"
        result_text += "p < 0.05 表示两个模型的性能存在显著差异"

        text_widget.insert(tk.END, result_text)
        text_widget.config(state=tk.DISABLED)

        # 添加关闭按钮
        button_frame = ttk.Frame(result_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="关闭",
                  command=result_window.destroy).pack(side=tk.RIGHT, padx=5)

    def _create_delong_module(self):
        """创建DeLong检验模块（已存在，无需创建）"""
        self.gui.log_message("DeLong检验模块已存在")
