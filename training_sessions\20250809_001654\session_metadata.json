{"session_id": "20250809_001654", "session_name": "训练_nodule2_20250809_001654", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-09T00:16:54.441035", "last_modified": "2025-08-09T00:16:56.213918", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_001654.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\DecisionTree_single_001654.joblib", "save_time": "2025-08-09T00:16:54.487030"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_001654.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\RandomForest_single_001654.joblib", "save_time": "2025-08-09T00:16:54.586964"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_001654.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\XGBoost_single_001654.joblib", "save_time": "2025-08-09T00:16:54.685169"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_001654.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\LightGBM_single_001654.joblib", "save_time": "2025-08-09T00:16:54.792336"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_001655.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\CatBoost_single_001655.joblib", "save_time": "2025-08-09T00:16:55.838499"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_001655.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\Logistic_single_001655.joblib", "save_time": "2025-08-09T00:16:55.872736"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_001655.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\SVM_single_001655.joblib", "save_time": "2025-08-09T00:16:55.906102"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_001655.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\KNN_single_001655.joblib", "save_time": "2025-08-09T00:16:55.936720"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_001655.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\NaiveBayes_single_001655.joblib", "save_time": "2025-08-09T00:16:55.965548"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_001656.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_001654\\models\\NeuralNet_single_001656.joblib", "save_time": "2025-08-09T00:16:56.205731"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}