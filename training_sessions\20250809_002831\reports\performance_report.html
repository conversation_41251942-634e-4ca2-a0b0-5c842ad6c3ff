
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模型性能比较报告</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #34495e;
                margin-top: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f2f2f2;
            }
            .best-score {
                background-color: #2ecc71 !important;
                color: white;
                font-weight: bold;
            }
            .summary {
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }
            .metric-description {
                font-size: 0.9em;
                color: #7f8c8d;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: 2025-08-09 00:29:57</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型:</strong> KNN (综合得分: 0.682)</p>
                <p><strong>比较模型数量:</strong> 10</p>
                <p><strong>评估指标数量:</strong> 13</p>
            </div>
            
            <h2>🏆 模型排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                </tr>
    
                <tr>
                    <td>1</td>
                    <td><strong>KNN</strong></td>
                    <td class="best-score">0.682</td>
                    <td>0.775</td>
                    <td>0.643</td>
                    <td>0.692</td>
                    <td>0.667</td>
                    <td>0.789</td>
                </tr>
        
                <tr>
                    <td>2</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.657</td>
                    <td>0.775</td>
                    <td>0.667</td>
                    <td>0.615</td>
                    <td>0.640</td>
                    <td>0.744</td>
                </tr>
        
                <tr>
                    <td>3</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.622</td>
                    <td>0.750</td>
                    <td>0.636</td>
                    <td>0.538</td>
                    <td>0.583</td>
                    <td>0.776</td>
                </tr>
        
                <tr>
                    <td>4</td>
                    <td><strong>CatBoost</strong></td>
                    <td class="">0.622</td>
                    <td>0.750</td>
                    <td>0.636</td>
                    <td>0.538</td>
                    <td>0.583</td>
                    <td>0.775</td>
                </tr>
        
                <tr>
                    <td>5</td>
                    <td><strong>Logistic</strong></td>
                    <td class="">0.620</td>
                    <td>0.750</td>
                    <td>0.636</td>
                    <td>0.538</td>
                    <td>0.583</td>
                    <td>0.764</td>
                </tr>
        
                <tr>
                    <td>6</td>
                    <td><strong>LightGBM</strong></td>
                    <td class="">0.572</td>
                    <td>0.725</td>
                    <td>0.600</td>
                    <td>0.462</td>
                    <td>0.522</td>
                    <td>0.744</td>
                </tr>
        
                <tr>
                    <td>7</td>
                    <td><strong>XGBoost</strong></td>
                    <td class="">0.570</td>
                    <td>0.725</td>
                    <td>0.600</td>
                    <td>0.462</td>
                    <td>0.522</td>
                    <td>0.735</td>
                </tr>
        
                <tr>
                    <td>8</td>
                    <td><strong>DecisionTree</strong></td>
                    <td class="">0.543</td>
                    <td>0.700</td>
                    <td>0.545</td>
                    <td>0.462</td>
                    <td>0.500</td>
                    <td>0.719</td>
                </tr>
        
                <tr>
                    <td>9</td>
                    <td><strong>NaiveBayes</strong></td>
                    <td class="">0.503</td>
                    <td>0.700</td>
                    <td>0.571</td>
                    <td>0.308</td>
                    <td>0.400</td>
                    <td>0.749</td>
                </tr>
        
                <tr>
                    <td>10</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="">0.484</td>
                    <td>0.675</td>
                    <td>0.500</td>
                    <td>0.308</td>
                    <td>0.381</td>
                    <td>0.786</td>
                </tr>
        
            </table>
            
            <h2>📈 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    <th>准确率</th><th>精确率</th><th>召回率</th><th>F1分数</th><th>特异性</th><th>敏感性</th><th>阴性预测值</th><th>阳性预测值</th><th>AUC-ROC</th><th>AUC-PR</th><th>MCC</th><th>Kappa</th><th>平衡准确率</th></tr><tr><td><strong>CatBoost</strong></td><td class="">0.750</td><td class="">0.636</td><td class="">0.538</td><td class="">0.583</td><td class="">0.852</td><td class="">0.538</td><td class="">0.793</td><td class="">0.636</td><td class="">0.775</td><td class="">0.614</td><td class="">0.409</td><td class="">0.407</td><td class="">0.695</td></tr><tr><td><strong>RandomForest</strong></td><td class="">0.750</td><td class="">0.636</td><td class="">0.538</td><td class="">0.583</td><td class="">0.852</td><td class="">0.538</td><td class="">0.793</td><td class="">0.636</td><td class="">0.776</td><td class="">0.601</td><td class="">0.409</td><td class="">0.407</td><td class="">0.695</td></tr><tr><td><strong>XGBoost</strong></td><td class="">0.725</td><td class="">0.600</td><td class="">0.462</td><td class="">0.522</td><td class="">0.852</td><td class="">0.462</td><td class="">0.767</td><td class="">0.600</td><td class="">0.735</td><td class="">0.625</td><td class="">0.339</td><td class="">0.333</td><td class="">0.657</td></tr><tr><td><strong>KNN</strong></td><td class="best-score">0.775</td><td class="">0.643</td><td class="best-score">0.692</td><td class="best-score">0.667</td><td class="">0.815</td><td class="best-score">0.692</td><td class="best-score">0.846</td><td class="">0.643</td><td class="best-score">0.789</td><td class="">0.617</td><td class="best-score">0.498</td><td class="best-score">0.497</td><td class="best-score">0.754</td></tr><tr><td><strong>NaiveBayes</strong></td><td class="">0.700</td><td class="">0.571</td><td class="">0.308</td><td class="">0.400</td><td class="best-score">0.889</td><td class="">0.308</td><td class="">0.727</td><td class="">0.571</td><td class="">0.749</td><td class="">0.624</td><td class="">0.242</td><td class="">0.223</td><td class="">0.598</td></tr><tr><td><strong>Logistic</strong></td><td class="">0.750</td><td class="">0.636</td><td class="">0.538</td><td class="">0.583</td><td class="">0.852</td><td class="">0.538</td><td class="">0.793</td><td class="">0.636</td><td class="">0.764</td><td class="">0.650</td><td class="">0.409</td><td class="">0.407</td><td class="">0.695</td></tr><tr><td><strong>NeuralNet</strong></td><td class="">0.675</td><td class="">0.500</td><td class="">0.308</td><td class="">0.381</td><td class="">0.852</td><td class="">0.308</td><td class="">0.719</td><td class="">0.500</td><td class="">0.786</td><td class="">0.667</td><td class="">0.187</td><td class="">0.177</td><td class="">0.580</td></tr><tr><td><strong>DecisionTree</strong></td><td class="">0.700</td><td class="">0.545</td><td class="">0.462</td><td class="">0.500</td><td class="">0.815</td><td class="">0.462</td><td class="">0.759</td><td class="">0.545</td><td class="">0.719</td><td class="">0.566</td><td class="">0.290</td><td class="">0.288</td><td class="">0.638</td></tr><tr><td><strong>LightGBM</strong></td><td class="">0.725</td><td class="">0.600</td><td class="">0.462</td><td class="">0.522</td><td class="">0.852</td><td class="">0.462</td><td class="">0.767</td><td class="">0.600</td><td class="">0.744</td><td class="best-score">0.671</td><td class="">0.339</td><td class="">0.333</td><td class="">0.657</td></tr><tr><td><strong>SVM</strong></td><td class="best-score">0.775</td><td class="best-score">0.667</td><td class="">0.615</td><td class="">0.640</td><td class="">0.852</td><td class="">0.615</td><td class="">0.821</td><td class="best-score">0.667</td><td class="">0.744</td><td class="">0.650</td><td class="">0.478</td><td class="">0.477</td><td class="">0.734</td></tr>
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC-ROC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于综合性能评估，我们推荐使用 <strong>KNN</strong> 模型进行后续任务。</p>
                <p>如果您对特定指标有具体要求，请参考详细性能指标表格选择最合适的模型。</p>
            </div>
        </div>
    </body>
    </html>
    