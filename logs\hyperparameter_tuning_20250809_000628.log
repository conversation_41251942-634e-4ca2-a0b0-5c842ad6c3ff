2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.6436
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.6969
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7331
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - Trial 28: 发现更好的得分 0.7387
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 9, 'min_samples_split': 37, 'min_samples_leaf': 21, 'criterion': 'entropy', 'class_weight': None, 'max_features': None}
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.7387
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 实际执行试验次数: 29/50
2025-08-09 00:06:29 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:06:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_000629.html
2025-08-09 00:06:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_000630.html
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.36 秒
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:06:32 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.7026
2025-08-09 00:06:32 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.7100
2025-08-09 00:06:33 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8091
2025-08-09 00:06:33 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.8125
2025-08-09 00:06:38 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8436
2025-08-09 00:06:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 242, 'max_depth': 32, 'min_samples_split': 12, 'min_samples_leaf': 2, 'max_features': 'log2'}
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8436
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:06:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_000648.html
2025-08-09 00:06:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_000648.html
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.49 秒
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:48 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8915
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:49 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:50 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 56, 'max_depth': 4, 'learning_rate': 0.0964175830526111, 'subsample': 0.8323868634726811, 'colsample_bytree': 0.5093044318501301}
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.8915
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:06:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_000652.html
2025-08-09 00:06:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_000652.html
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.04 秒
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:06:52 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:07:00 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8812
2025-08-09 00:07:00 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8919
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 268, 'max_depth': 4, 'learning_rate': 0.2511203898118411, 'feature_fraction': 0.8099857781688787, 'bagging_fraction': 0.6074770975788601}
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.8927
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:07:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_000702.html
2025-08-09 00:07:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_000702.html
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.95 秒
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:07:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:07:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8051
2025-08-09 00:07:09 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.8411
2025-08-09 00:07:17 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.8656
2025-08-09 00:07:22 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8846
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8846
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 247, 'depth': 3, 'learning_rate': 0.15912798713994736, 'l2_leaf_reg': 6.331731119758382, 'bagging_temperature': 0.046450412719997725}
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.8846
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_000817.html
2025-08-09 00:08:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_000817.html
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 74.89 秒
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:17 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 3.1148748233364714, 'clf__solver': 'liblinear'}
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.7930
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_000818.html
2025-08-09 00:08:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_000818.html
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.11 秒
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:18 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.7536
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.7559
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.7930
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.041232043021419, 'clf__kernel': 'linear'}
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.7980
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 00:08:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_000819.html
2025-08-09 00:08:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_000820.html
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.61 秒
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 3}
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.7526
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:08:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_000821.html
2025-08-09 00:08:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_000821.html
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.36 秒
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.7259
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:08:21 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:08:37 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.8344
2025-08-09 00:08:37 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8408
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:08:58 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.009969290438929903}
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.8408
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 00:09:03 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:09:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:09:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_000903.html
2025-08-09 00:09:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:09:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_000904.html
2025-08-09 00:09:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 42.59 秒
