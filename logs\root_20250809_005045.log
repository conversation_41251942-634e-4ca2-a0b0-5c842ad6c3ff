2025-08-09 00:50:47 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 00:50:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 00:50:47 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 00:50:48 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 00:50:48 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 00:50:48 - GUI - INFO - GUI界面初始化完成
2025-08-09 00:51:45 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 12)
2025-08-09 00:51:45 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 100
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.8984
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9132
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9224
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 14: 发现更好的得分 0.9280
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.9515
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9524
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 5, 'min_samples_split': 13, 'min_samples_leaf': 5, 'criterion': 'entropy', 'class_weight': None, 'max_features': 'log2'}
2025-08-09 00:51:45 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9524
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 实际执行试验次数: 35/100
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:51:46 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:51:46 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_005146.html
2025-08-09 00:51:46 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:51:46 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_005146.html
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.55 秒
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:51:46 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:51:49 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9280
2025-08-09 00:51:51 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9465
2025-08-09 00:51:57 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9490
2025-08-09 00:51:59 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9632
2025-08-09 00:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9632
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 75, 'max_depth': 18, 'min_samples_split': 9, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9640
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/100
2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:52:07 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:52:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_005207.html
2025-08-09 00:52:07 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:52:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_005207.html
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.62 秒
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:08 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9824
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:09 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9849
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:10 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9883
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:11 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9883
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 278, 'max_depth': 8, 'learning_rate': 0.08116897882912005, 'subsample': 0.995707631782476, 'colsample_bytree': 0.7975094059918901}
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9891
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 31/100
2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:52:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:52:14 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_005214.html
2025-08-09 00:52:14 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:52:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_005214.html
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 7.03 秒
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:52:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:52:22 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9893
2025-08-09 00:52:23 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9909
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9909
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9909
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 92, 'max_depth': 4, 'learning_rate': 0.2946045971142018, 'feature_fraction': 0.8564906369580745, 'bagging_fraction': 0.5002411648996792}
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9916
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 27/100
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:52:24 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:52:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_005224.html
2025-08-09 00:52:24 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:52:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_005224.html
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.47 秒
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 100
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 00:52:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 00:52:29 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9671
2025-08-09 00:52:31 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9749
2025-08-09 00:52:39 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9815
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9815
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/100
2025-08-09 00:53:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:24 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_005324.html
2025-08-09 00:53:25 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_005325.html
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 60.58 秒
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9664
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9680
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 6.240874349180085, 'clf__solver': 'liblinear'}
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9689
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/100
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:26 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_005326.html
2025-08-09 00:53:26 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_005326.html
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.41 秒
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 100
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:26 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9785
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9826
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9826
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - Trial 18: 发现更好的得分 0.9859
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 9.99020508064756, 'clf__kernel': 'rbf'}
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9868
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/100
2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:27 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:27 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_005327.html
2025-08-09 00:53:27 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_005327.html
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.35 秒
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 100
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9228
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9276
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 7}
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9295
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-09 00:53:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:28 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_005328.html
2025-08-09 00:53:29 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:29 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_005329.html
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.31 秒
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 100
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.8995
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 100
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 00:53:29 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 00:53:39 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9795
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.0033209978031396824}
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9795
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 00:53:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_005353.html
2025-08-09 00:53:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 00:53:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_005353.html
2025-08-09 00:53:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 24.35 秒
2025-08-09 00:54:20 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420
2025-08-09 00:54:20 - training_session_manager - INFO - 创建训练会话: 训练_N-3_20250809_005420 (ID: 20250809_005420)
2025-08-09 00:54:20 - training_session_manager - INFO - 创建新会话: 训练_N-3_20250809_005420
2025-08-09 00:54:20 - session_utils - INFO - 创建新会话: 训练_N-3_20250809_005420 (ID: 20250809_005420)
2025-08-09 00:54:20 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 12)
2025-08-09 00:54:20 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 00:54:20 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:54:20 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:54:20 - model_training - INFO - AUC: 0.9309
2025-08-09 00:54:20 - model_training - INFO - AUPRC: 0.8875
2025-08-09 00:54:20 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:20 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-09 00:54:20 - model_training - INFO - 
分类报告:
2025-08-09 00:54:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-09 00:54:20 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:20 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8750
2025-08-09 00:54:20 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\DecisionTree_single_005420.joblib
2025-08-09 00:54:20 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\DecisionTree_single_005420.joblib
2025-08-09 00:54:20 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:54:20 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:54:20 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:54:20 - model_training - INFO - AUC: 0.9399
2025-08-09 00:54:20 - model_training - INFO - AUPRC: 0.9432
2025-08-09 00:54:20 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:20 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:54:20 - model_training - INFO - 
分类报告:
2025-08-09 00:54:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:54:20 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:54:20 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 00:54:21 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\RandomForest_single_005420.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\RandomForest_single_005420.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:54:21 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:54:21 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:54:21 - model_training - INFO - 准确率: 0.9250
2025-08-09 00:54:21 - model_training - INFO - AUC: 0.9540
2025-08-09 00:54:21 - model_training - INFO - AUPRC: 0.8766
2025-08-09 00:54:21 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:21 - model_training - INFO - 
[[22  1]
 [ 2 15]]
2025-08-09 00:54:21 - model_training - INFO - 
分类报告:
2025-08-09 00:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.96      0.94        23
           1       0.94      0.88      0.91        17

    accuracy                           0.93        40
   macro avg       0.93      0.92      0.92        40
weighted avg       0.93      0.93      0.92        40

2025-08-09 00:54:21 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:54:21 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9250
2025-08-09 00:54:21 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\XGBoost_single_005421.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\XGBoost_single_005421.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:54:21 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:54:21 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:54:21 - model_training - INFO - 准确率: 0.9250
2025-08-09 00:54:21 - model_training - INFO - AUC: 0.9770
2025-08-09 00:54:21 - model_training - INFO - AUPRC: 0.9665
2025-08-09 00:54:21 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:21 - model_training - INFO - 
[[22  1]
 [ 2 15]]
2025-08-09 00:54:21 - model_training - INFO - 
分类报告:
2025-08-09 00:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.96      0.94        23
           1       0.94      0.88      0.91        17

    accuracy                           0.93        40
   macro avg       0.93      0.92      0.92        40
weighted avg       0.93      0.93      0.92        40

2025-08-09 00:54:21 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:54:21 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9250
2025-08-09 00:54:21 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\LightGBM_single_005421.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\LightGBM_single_005421.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:54:21 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:54:22 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.9250
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9821
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.9795
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[22  1]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.96      0.94        23
           1       0.94      0.88      0.91        17

    accuracy                           0.93        40
   macro avg       0.93      0.92      0.92        40
weighted avg       0.93      0.93      0.92        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 1.11 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9250
2025-08-09 00:54:22 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\CatBoost_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\CatBoost_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9207
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.9287
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8500
2025-08-09 00:54:22 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\Logistic_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\Logistic_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: SVM
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9003
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.9003
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[17  6]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.89      0.74      0.81        23
           1       0.71      0.88      0.79        17

    accuracy                           0.80        40
   macro avg       0.80      0.81      0.80        40
weighted avg       0.82      0.80      0.80        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 SVM 性能: 准确率=0.8000
2025-08-09 00:54:22 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\SVM_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\SVM_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: KNN
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.8747
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.7775
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 KNN 性能: 准确率=0.8250
2025-08-09 00:54:22 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\KNN_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\KNN_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.8555
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.7533
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.7500
2025-08-09 00:54:22 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\NaiveBayes_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\NaiveBayes_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9335
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.8779
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.21 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 00:54:22 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\NeuralNet_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\NeuralNet_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
